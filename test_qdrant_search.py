#!/usr/bin/env python3
"""
Test script to verify Qdrant collections are working properly
"""

from qdrant_client import QdrantClient
import ollama
from sentence_transformers import SentenceTransformer

def test_collections():
    client = QdrantClient(url="http://localhost:6333")
    
    # Test queries
    test_queries = [
        "church administration procedures",
        "hall hire booking process", 
        "finance procedures",
        "templater automation setup"
    ]
    
    print("🔍 TESTING QDRANT COLLECTIONS")
    print("=" * 50)
    
    # Test sentence-transformers collection
    print("\n📊 SENTENCE-TRANSFORMERS COLLECTION")
    print("-" * 40)
    
    try:
        model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
        
        for query in test_queries:
            print(f"\n🔍 Query: '{query}'")
            
            # Generate embedding
            query_embedding = model.encode(query).tolist()
            
            # Search
            results = client.search(
                collection_name="cruca-documentation",
                query_vector=query_embedding,
                limit=2
            )
            
            if results:
                for i, result in enumerate(results, 1):
                    file_path = result.payload.get('file_path', 'Unknown')
                    score = result.score
                    print(f"  {i}. {file_path} (score: {score:.3f})")
            else:
                print("  No results found")
                
    except Exception as e:
        print(f"❌ Error testing sentence-transformers: {e}")
    
    # Test Qwen3 collection
    print("\n📊 QWEN3-EMBEDDING COLLECTION")
    print("-" * 40)
    
    try:
        for query in test_queries:
            print(f"\n🔍 Query: '{query}'")
            
            # Generate embedding with Ollama
            response = ollama.embeddings(
                model="dengcao/Qwen3-Embedding-0.6B:Q8_0", 
                prompt=query
            )
            query_embedding = response['embedding']
            
            # Search
            results = client.search(
                collection_name="cruca-documentation-qwen3",
                query_vector=query_embedding,
                limit=2
            )
            
            if results:
                for i, result in enumerate(results, 1):
                    file_path = result.payload.get('file_path', 'Unknown')
                    score = result.score
                    print(f"  {i}. {file_path} (score: {score:.3f})")
            else:
                print("  No results found")
                
    except Exception as e:
        print(f"❌ Error testing Qwen3: {e}")
    
    print("\n✅ Collection testing complete!")

if __name__ == "__main__":
    test_collections()
