# Pandoc vs Obsidian Built-in PDF Export

## 🏆 Quick Answer: Use Pandoc Plugin!

**For your 100+ page Office Procedures Manual with complex internal links, the Obsidian Pandoc Plugin is MUCH better.**

---

## Comparison Table

| Feature | Obsidian Built-in | Pandoc Plugin |
|---------|------------------|---------------|
| **Internal Links** | Often broken | ✅ Works perfectly |
| **Table of Contents** | Basic | ✅ Professional with page numbers |
| **PDF Quality** | Good | ✅ Excellent (LaTeX typesetting) |
| **Customization** | Limited | ✅ Extensive |
| **Page Breaks** | Basic | ✅ Full control |
| **Headers/Footers** | No | ✅ Yes |
| **Page Numbers** | No | ✅ Yes |
| **Cross-references** | Limited | ✅ Full support |
| **Speed** | Fast | Slower (but worth it) |

---

## Setup Instructions

### Step 1: Install Pandoc on Windows

```powershell
# Method A: Using Chocolatey (if installed)
choco install pandoc

# Method B: Using winget
winget install --source winget --exact --id JohnMacFarlane.Pandoc

# Method C: Direct Download
# Go to: https://pandoc.org/installing.html
# Download the Windows installer (.msi file)
# Run the installer
```

### Step 2: Install LaTeX (for best PDF output)

```powershell
# Option 1: MiKTeX (Recommended - smaller, installs packages as needed)
winget install MiKTeX.MiKTeX

# Option 2: TeX Live (Complete - larger download)
# Download from: https://www.tug.org/texlive/

# Option 3: TinyTeX (Minimal - if space is concern)
# Download from: https://yihui.org/tinytex/
```

### Step 3: Install Obsidian Pandoc Plugin

1. Open Obsidian Settings (Ctrl+,)
2. Go to "Community plugins"
3. Click "Browse"
4. Search for "Pandoc"
5. Install "Obsidian Pandoc Plugin" by Oliver Balfour
6. Enable the plugin

### Step 4: Configure Pandoc Plugin

1. Go to Settings → Pandoc Plugin
2. Configure these settings:

```yaml
Pandoc Path: (should auto-detect, or set to C:\Program Files\Pandoc\pandoc.exe)
PDFLaTeX Path: (should auto-detect)

Export Settings:
- Export folder: Same as current file
- Export format: PDF

Extra Pandoc Arguments:
--toc
--toc-depth=3
--number-sections
--pdf-engine=xelatex
-V geometry:margin=1in
-V fontsize=11pt
-V linkcolor=blue
-V urlcolor=blue
-V toccolor=black
```

---

## Optimized Export Commands

### For Your Office Procedures Manual

Create a file called `pandoc-config.yaml` in the Office-Procedures folder:

```yaml
# pandoc-config.yaml
metadata:
  title: "CRUCA Office Procedures Manual"
  author: "Caboolture Region Uniting Church Australia"
  date: "2024"
  toc: true
  toc-depth: 3
  numbersections: true
  
variables:
  documentclass: report
  fontsize: 11pt
  geometry:
    - margin=1in
    - left=1.25in  # Extra for binding
  linkcolor: blue
  urlcolor: blue
  toccolor: black
  
  # Professional fonts (if available)
  mainfont: "Calibri"
  sansfont: "Arial"
  monofont: "Consolas"
  
  # Headers and footers
  header-includes: |
    \usepackage{fancyhdr}
    \pagestyle{fancy}
    \fancyhead[L]{CRUCA Office Procedures}
    \fancyhead[R]{\thepage}
    \fancyfoot[C]{Internal Use Only}
```

### PowerShell Export Command

```powershell
# Basic export with TOC and links
pandoc Office-Procedures-Manual.md -o Office-Procedures-Manual.pdf `
  --toc `
  --toc-depth=3 `
  --number-sections `
  --pdf-engine=xelatex `
  -V linkcolor=blue

# Professional export with all features
pandoc Office-Procedures-Manual.md -o Office-Procedures-Manual-Professional.pdf `
  --metadata-file=pandoc-config.yaml `
  --toc `
  --toc-depth=3 `
  --number-sections `
  --pdf-engine=xelatex `
  --highlight-style=tango `
  --template=default
```

---

## Why Pandoc Works Better for Links

### The Technical Reason:
- **Obsidian built-in**: Converts MD → HTML → PDF (links often break in conversion)
- **Pandoc**: Converts MD → LaTeX → PDF (preserves document structure)

### Benefits for Your Manual:
1. ✅ **All internal links work** - TOC links jump to correct pages
2. ✅ **Professional appearance** - Proper typography and spacing
3. ✅ **Page numbers** - In TOC and footer
4. ✅ **Bookmarks** - PDF bookmarks for navigation
5. ✅ **Hyperref package** - Advanced PDF features

---

## Quick Test

After installation, test with:

```powershell
# Check installation
pandoc --version
pdflatex --version

# Quick test export
echo "# Test`nThis is a test with [a link](#test)" | pandoc -o test.pdf --pdf-engine=xelatex
```

---

## Troubleshooting

### "Pandoc not found"
```powershell
# Add to PATH manually
$env:Path += ";C:\Program Files\Pandoc"
# Restart Obsidian
```

### "PDF engine not found"
```powershell
# Install MiKTeX or use HTML engine
pandoc input.md -o output.pdf --pdf-engine=html
```

### Links still not working
```powershell
# Force hyperref package
pandoc input.md -o output.pdf -V linkcolor=blue -V colorlinks=true
```

---

## 🎯 Recommended Workflow for Your Manual

1. **Use the Pandoc Plugin in Obsidian**:
   - Right-click on your .md file
   - Select "Pandoc Plugin: Export as PDF"
   - Links will work!

2. **Or use PowerShell for batch processing**:
   ```powershell
   # Export with all features
   pandoc Office-Procedures-Manual.md `
     -o "CRUCA-Office-Procedures-$(Get-Date -Format 'yyyy-MM-dd').pdf" `
     --toc --toc-depth=3 `
     --number-sections `
     --pdf-engine=xelatex `
     -V geometry:"left=1.25in,right=1in,top=1in,bottom=1in" `
     -V linkcolor=blue
   ```

3. **For printing**:
   - Use the PDF with 1.25" left margin for binding
   - Print double-sided if desired
   - The TOC with page numbers helps navigation

---

## Final Verdict

**For your use case (complex manual with TOC and internal links):**

### Use Pandoc Plugin because:
- ✅ Internal links work reliably
- ✅ Professional PDF output
- ✅ Page numbers and proper TOC
- ✅ Better for printing
- ✅ More control over formatting

### Skip Obsidian built-in for:
- ❌ Complex documents
- ❌ Documents with many internal links
- ❌ Professional printing needs

The setup time for Pandoc is worth it for the quality improvement!
