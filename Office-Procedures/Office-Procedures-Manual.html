<!doctype html>
<html>
    <head>
        <title>Office-Procedures-Manual</title>
        <meta charset='utf-8'/>
        <style>
 .ͼ1.cm-focused {outline: 1px dotted #212121;}
.ͼ1 {position: relative !important; box-sizing: border-box; display: flex !important; flex-direction: column;}
.ͼ1 .cm-scroller {display: flex !important; align-items: flex-start !important; font-family: monospace; line-height: 1.4; height: 100%; overflow-x: auto; position: relative; z-index: 0; overflow-anchor: none;}
.ͼ1 .cm-content[contenteditable=true] {-webkit-user-modify: read-write-plaintext-only;}
.ͼ1 .cm-content {margin: 0; flex-grow: 2; flex-shrink: 0; display: block; white-space: pre; word-wrap: normal; box-sizing: border-box; min-height: 100%; padding: 4px 0; outline: none;}
.ͼ1 .cm-lineWrapping {white-space: pre-wrap; white-space: break-spaces; word-break: break-word; overflow-wrap: anywhere; flex-shrink: 1;}
.ͼ2 .cm-content {caret-color: black;}
.ͼ3 .cm-content {caret-color: white;}
.ͼ1 .cm-line {display: block; padding: 0 2px 0 6px;}
.ͼ1 .cm-layer > * {position: absolute;}
.ͼ1 .cm-layer {position: absolute; left: 0; top: 0; contain: size style;}
.ͼ2 .cm-selectionBackground {background: #d9d9d9;}
.ͼ3 .cm-selectionBackground {background: #222;}
.ͼ2.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground {background: #d7d4f0;}
.ͼ3.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground {background: #233;}
.ͼ1 .cm-cursorLayer {pointer-events: none;}
.ͼ1.cm-focused > .cm-scroller > .cm-cursorLayer {animation: steps(1) cm-blink 1.2s infinite;}
@keyframes cm-blink {50% {opacity: 0;}}
@keyframes cm-blink2 {50% {opacity: 0;}}
.ͼ1 .cm-cursor, .ͼ1 .cm-dropCursor {border-left: 1.2px solid black; margin-left: -0.6px; pointer-events: none;}
.ͼ1 .cm-cursor {display: none;}
.ͼ3 .cm-cursor {border-left-color: #ddd;}
.ͼ1 .cm-dropCursor {position: absolute;}
.ͼ1.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor {display: block;}
.ͼ1 .cm-iso {unicode-bidi: isolate;}
.ͼ1 .cm-announced {position: fixed; top: -10000px;}
@media print {.ͼ1 .cm-announced {display: none;}}
.ͼ2 .cm-activeLine {background-color: #cceeff44;}
.ͼ3 .cm-activeLine {background-color: #99eeff33;}
.ͼ2 .cm-specialChar {color: red;}
.ͼ3 .cm-specialChar {color: #f78;}
.ͼ1 .cm-gutters {flex-shrink: 0; display: flex; height: 100%; box-sizing: border-box; inset-inline-start: 0; z-index: 200;}
.ͼ2 .cm-gutters {background-color: #f5f5f5; color: #6c6c6c; border-right: 1px solid #ddd;}
.ͼ3 .cm-gutters {background-color: #333338; color: #ccc;}
.ͼ1 .cm-gutter {display: flex !important; flex-direction: column; flex-shrink: 0; box-sizing: border-box; min-height: 100%; overflow: hidden;}
.ͼ1 .cm-gutterElement {box-sizing: border-box;}
.ͼ1 .cm-lineNumbers .cm-gutterElement {padding: 0 3px 0 5px; min-width: 20px; text-align: right; white-space: nowrap;}
.ͼ2 .cm-activeLineGutter {background-color: #e2f2ff;}
.ͼ3 .cm-activeLineGutter {background-color: #222227;}
.ͼ1 .cm-panels {box-sizing: border-box; position: sticky; left: 0; right: 0; z-index: 300;}
.ͼ2 .cm-panels {background-color: #f5f5f5; color: black;}
.ͼ2 .cm-panels-top {border-bottom: 1px solid #ddd;}
.ͼ2 .cm-panels-bottom {border-top: 1px solid #ddd;}
.ͼ3 .cm-panels {background-color: #333338; color: white;}
.ͼ1 .cm-tab {display: inline-block; overflow: hidden; vertical-align: bottom;}
.ͼ1 .cm-widgetBuffer {vertical-align: text-top; height: 1em; width: 0; display: inline;}
.ͼ1 .cm-placeholder {color: #888; display: inline-block; vertical-align: top;}
.ͼ1 .cm-highlightSpace {background-image: radial-gradient(circle at 50% 55%, #aaa 20%, transparent 5%); background-position: center;}
.ͼ1 .cm-highlightTab {background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>'); background-size: auto 100%; background-position: right 90%; background-repeat: no-repeat;}
.ͼ1 .cm-trailingSpace {background-color: #ff332255;}
.ͼ1 .cm-button {vertical-align: middle; color: inherit; font-size: 70%; padding: .2em 1em; border-radius: 1px;}
.ͼ2 .cm-button:active {background-image: linear-gradient(#b4b4b4, #d0d3d6);}
.ͼ2 .cm-button {background-image: linear-gradient(#eff1f5, #d9d9df); border: 1px solid #888;}
.ͼ3 .cm-button:active {background-image: linear-gradient(#111, #333);}
.ͼ3 .cm-button {background-image: linear-gradient(#393939, #111); border: 1px solid #888;}
.ͼ1 .cm-textfield {vertical-align: middle; color: inherit; font-size: 70%; border: 1px solid silver; padding: .2em .5em;}
.ͼ2 .cm-textfield {background-color: white;}
.ͼ3 .cm-textfield {border: 1px solid #555; background-color: inherit;}
.ͼ1 .cm-foldPlaceholder {background-color: #eee; border: 1px solid #ddd; color: #888; border-radius: .2em; margin: 0 1px; padding: 0 1px; cursor: pointer;}
.ͼ1 .cm-foldGutter span {padding: 0 1px; cursor: pointer;}
.ͼp .cm-vimMode .cm-cursorLayer:not(.cm-vimCursorLayer) {display: none;}
.ͼp .cm-vim-panel {padding: 0px 10px; font-family: monospace; min-height: 1.3em;}
.ͼp .cm-vim-panel input {background: transparent; border: none; outline: none;}
.ͼo .cm-vimMode .cm-line {caret-color: transparent !important;}
.ͼo .cm-fat-cursor {position: absolute; border: none; white-space: pre;}
.ͼo.cm-focused > .cm-scroller > .cm-cursorLayer > .cm-fat-cursor {background: var(--interactive-accent); color: var(--text-on-accent);}
.ͼo:not(.cm-focused) > .cm-scroller > .cm-cursorLayer > .cm-fat-cursor {color: transparent !important;}
 @keyframes loading {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.workspace-leaf-content[data-type="git-view"] .button-border {
    border: 2px solid var(--interactive-accent);
    border-radius: var(--radius-s);
}

.workspace-leaf-content[data-type="git-view"] .view-content {
    padding: 0;
}

.workspace-leaf-content[data-type="git-history-view"] .view-content {
    padding: 0;
}

.loading > svg {
    animation: 2s linear infinite loading;
    transform-origin: 50% 50%;
    display: inline-block;
}

.obsidian-git-center {
    margin: auto;
    text-align: center;
    width: 50%;
}

.obsidian-git-textarea {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.obsidian-git-disabled {
    opacity: 0.5;
}

.obsidian-git-center-button {
    display: block;
    margin: 20px auto;
}

.tooltip.mod-left {
    overflow-wrap: break-word;
}

.tooltip.mod-right {
    overflow-wrap: break-word;
}
.git-tools {
    display: flex;
    margin-left: auto;
}
.git-tools .type {
    padding-left: var(--size-2-1);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 11px;
}

.git-tools .type[data-type="M"] {
    color: orange;
}
.git-tools .type[data-type="D"] {
    color: red;
}
.git-tools .buttons {
    display: flex;
}
.git-tools .buttons > * {
    padding: 0 0;
    height: auto;
}

.is-active .git-tools .buttons > * {
    color: var(--nav-item-color-active);
}

.git-author {
    color: var(--text-accent);
}

.git-date {
    color: var(--text-accent);
}

.git-ref {
    color: var(--text-accent);
}

.workspace-leaf-content[data-type="diff-view"] .d2h-d-none {
    display: none;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-wrapper {
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-header {
    background-color: var(--background-primary);
    border-bottom: 1px solid var(--interactive-accent);
    font-family: var(--font-monospace);
    height: 35px;
    padding: 5px 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-header,
.workspace-leaf-content[data-type="diff-view"] .d2h-file-stats {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-stats {
    font-size: 14px;
    margin-left: auto;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-lines-added {
    border: 1px solid #b4e2b4;
    border-radius: 5px 0 0 5px;
    color: #399839;
    padding: 2px;
    text-align: right;
    vertical-align: middle;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-lines-deleted {
    border: 1px solid #e9aeae;
    border-radius: 0 5px 5px 0;
    color: #c33;
    margin-left: 1px;
    padding: 2px;
    text-align: left;
    vertical-align: middle;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-name-wrapper {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-size: 15px;
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-name {
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-wrapper {
    border: 1px solid var(--background-modifier-border);
    border-radius: 3px;
    margin-bottom: 1em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border: 1px solid var(--background-modifier-border);
    border-radius: 3px;
    cursor: pointer;
    display: none;
    font-size: 12px;
    justify-content: flex-end;
    padding: 4px 8px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse.d2h-selected {
    background-color: #c8e1ff;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse-input {
    margin: 0 4px 0 0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-diff-table {
    border-collapse: collapse;
    font-family: Menlo, Consolas, monospace;
    font-size: 13px;
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-files-diff {
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-diff {
    overflow-y: hidden;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-side-diff {
    display: inline-block;
    margin-bottom: -8px;
    margin-right: -4px;
    overflow-x: scroll;
    overflow-y: hidden;
    width: 50%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line {
    padding: 0 8em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line {
    display: inline-block;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    white-space: nowrap;
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line {
    padding: 0 4.5em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line-ctn {
    word-wrap: normal;
    background: none;
    display: inline-block;
    padding: 0;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
    vertical-align: middle;
    white-space: pre;
    width: 100%;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    del {
    background-color: #ffb6ba;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    del {
    background-color: #8d232881;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line del,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line ins {
    border-radius: 0.2em;
    display: inline-block;
    margin-top: -1px;
    text-decoration: none;
    vertical-align: middle;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    ins {
    background-color: #97f295;
    text-align: left;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    ins {
    background-color: #1d921996;
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line-prefix {
    word-wrap: normal;
    background: none;
    display: inline;
    padding: 0;
    white-space: pre;
}

.workspace-leaf-content[data-type="diff-view"] .line-num1 {
    float: left;
}

.workspace-leaf-content[data-type="diff-view"] .line-num1,
.workspace-leaf-content[data-type="diff-view"] .line-num2 {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
    padding: 0 0.5em;
    text-overflow: ellipsis;
    width: 3.5em;
}

.workspace-leaf-content[data-type="diff-view"] .line-num2 {
    float: right;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber {
    background-color: var(--background-primary);
    border: solid var(--background-modifier-border);
    border-width: 0 1px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: var(--text-muted);
    cursor: pointer;
    display: inline-block;
    position: absolute;
    text-align: right;
    width: 7.5em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber:after {
    content: "\200b";
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber {
    background-color: var(--background-primary);
    border: solid var(--background-modifier-border);
    border-width: 0 1px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: var(--text-muted);
    cursor: pointer;
    display: inline-block;
    overflow: hidden;
    padding: 0 0.5em;
    position: absolute;
    text-align: right;
    text-overflow: ellipsis;
    width: 4em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-diff-tbody tr {
    position: relative;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber:after {
    content: "\200b";
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-emptyplaceholder,
.workspace-leaf-content[data-type="diff-view"] .d2h-emptyplaceholder {
    background-color: var(--background-primary);
    border-color: var(--background-modifier-border);
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line-prefix,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber,
.workspace-leaf-content[data-type="diff-view"] .d2h-emptyplaceholder {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber {
    direction: rtl;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-del {
    background-color: #fee8e9;
    border-color: #e9aeae;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-ins {
    background-color: #dfd;
    border-color: #b4e2b4;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-del {
    background-color: #521b1d83;
    border-color: #691d1d73;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-ins {
    background-color: rgba(30, 71, 30, 0.5);
    border-color: #13501381;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-info {
    background-color: var(--background-primary);
    border-color: var(--background-modifier-border);
    color: var(--text-normal);
}

.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-del.d2h-change {
    background-color: #fdf2d0;
}

.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-del.d2h-change {
    background-color: #55492480;
}

.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-ins.d2h-change {
    background-color: #ded;
}

.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-ins.d2h-change {
    background-color: rgba(37, 78, 37, 0.418);
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-wrapper {
    margin-bottom: 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-wrapper a {
    color: #3572b0;
    text-decoration: none;
}

.workspace-leaf-content[data-type="diff-view"]
    .d2h-file-list-wrapper
    a:visited {
    color: #3572b0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-header {
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-title {
    font-weight: 700;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-line {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list {
    display: block;
    list-style: none;
    margin: 0;
    padding: 0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list > li {
    border-bottom: 1px solid var(--background-modifier-border);
    margin: 0;
    padding: 5px 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list > li:last-child {
    border-bottom: none;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-switch {
    cursor: pointer;
    display: none;
    font-size: 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-icon {
    fill: currentColor;
    margin-right: 10px;
    vertical-align: middle;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-deleted {
    color: #c33;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-added {
    color: #399839;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-changed {
    color: #d0b44c;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-moved {
    color: #3572b0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-tag {
    background-color: var(--background-primary);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-size: 10px;
    margin-left: 5px;
    padding: 0 2px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-deleted-tag {
    border: 2px solid #c33;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-added-tag {
    border: 1px solid #399839;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-changed-tag {
    border: 1px solid #d0b44c;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-moved-tag {
    border: 1px solid #3572b0;
}

/* ====================== Line Authoring Information ====================== */

.cm-gutterElement.obs-git-blame-gutter {
    /* Add background color to spacing inbetween and around the gutter for better aesthetics */
    border-width: 0px 2px 0.2px 2px;
    border-style: solid;
    border-color: var(--background-secondary);
    background-color: var(--background-secondary);
}

.cm-gutterElement.obs-git-blame-gutter > div,
.line-author-settings-preview {
    /* delegate text color to settings */
    color: var(--obs-git-gutter-text);
    font-family: monospace;
    height: 100%; /* ensure, that age-based background color occupies entire parent */
    text-align: right;
    padding: 0px 6px 0px 6px;
    white-space: pre; /* Keep spaces and do not collapse them. */
}

@media (max-width: 800px) {
    /* hide git blame gutter not to superpose text */
    .cm-gutterElement.obs-git-blame-gutter {
        display: none;
    }
}

.git-unified-diff-view,
.git-split-diff-view .cm-deletedLine .cm-changedText {
    background-color: #ee443330;
}

.git-unified-diff-view,
.git-split-diff-view .cm-insertedLine .cm-changedText {
    background-color: #22bb2230;
}

/* Limits the scrollbar to the view body */
.git-view {
    display: flex;
    flex-direction: column;
    position: relative;
    height: 100%;
}

.git-obscure-prompt[git-is-obscured="true"] #git-show-password:after {
    -webkit-mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svg-icon lucide-eye"><path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path><circle cx="12" cy="12" r="3"></circle></svg>');
}

.git-obscure-prompt[git-is-obscured="false"] #git-show-password:after {
    -webkit-mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svg-icon lucide-eye-off"><path d="M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49"></path><path d="M14.084 14.158a3 3 0 0 1-4.242-4.242"></path><path d="M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143"></path><path d="m2 2 20 20"></path></svg>');
}

/* Override styling of Codemirror merge view "collapsed lines" indicator */
.git-split-diff-view .ͼ2 .cm-collapsedLines {
    background: var(--interactive-normal);
    border-radius: var(--radius-m);
    color: var(--text-accent);
    font-size: var(--font-small);
    padding: var(--size-4-1) var(--size-4-1);
}
.git-split-diff-view .ͼ2 .cm-collapsedLines:hover {
    background: var(--interactive-hover);
    color: var(--text-accent-hover);
}
 .templater_search {
    width: calc(100% - 20px);
}

.templater_div {
    border-top: 1px solid var(--background-modifier-border);
}

.templater_div > .setting-item {
    border-top: none !important;
    align-self: center;
}

.templater_div > .setting-item > .setting-item-control {
    justify-content: space-around;
    padding: 0;
    width: 100%;
}

.templater_div
    > .setting-item
    > .setting-item-control
    > .setting-editor-extra-setting-button {
    align-self: center;
}

.templater_donating {
    margin: 10px;
}

.templater_title {
    margin: 0;
    padding: 0;
    margin-top: 5px;
    text-align: center;
}

.templater_template {
    align-self: center;
    margin-left: 5px;
    margin-right: 5px;
    width: 70%;
}

.templater_cmd {
    margin-left: 5px;
    margin-right: 5px;
    font-size: 14px;
    width: 100%;
}

.templater_div2 > .setting-item {
    align-content: center;
    justify-content: center;
}

.templater-prompt-div {
    display: flex;
}

.templater-prompt-form {
    display: flex;
    flex-grow: 1;
}

.templater-prompt-input {
    flex-grow: 1;
}

.templater-button-div {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 1rem;
}

textarea.templater-prompt-input {
    height: 10rem;
}

textarea.templater-prompt-input:focus {
    border-color: var(--interactive-accent);
}

.cm-s-obsidian .templater-command-bg {
    left: 0px;
    right: 0px;
    background-color: var(--background-primary-alt);
}

.cm-s-obsidian .cm-templater-command {
    font-size: 0.85em;
    font-family: var(--font-monospace);
    line-height: 1.3;
}

.cm-s-obsidian .templater-inline .cm-templater-command {
    background-color: var(--background-primary-alt);
}

.cm-s-obsidian .cm-templater-command.cm-templater-opening-tag {
    font-weight: bold;
}

.cm-s-obsidian .cm-templater-command.cm-templater-closing-tag {
    font-weight: bold;
}

.cm-s-obsidian .cm-templater-command.cm-templater-interpolation-tag {
    color: var(--code-property, #008bff);
}

.cm-s-obsidian .cm-templater-command.cm-templater-execution-tag {
    color: var(--code-function, #c0d700);
}

.cm-s-obsidian .cm-templater-command.cm-keyword {
    color: var(--code-keyword, #00a7aa);
    font-weight: normal;
}

.cm-s-obsidian .cm-templater-command.cm-atom {
    color: var(--code-normal, #f39b35);
}

.cm-s-obsidian .cm-templater-command.cm-value,
.cm-s-obsidian .cm-templater-command.cm-number,
.cm-s-obsidian .cm-templater-command.cm-type {
    color: var(--code-value, #a06fca);
}

.cm-s-obsidian .cm-templater-command.cm-def,
.cm-s-obsidian .cm-templater-command.cm-type.cm-def {
    color: var(--code-normal, var(--text-normal));
}

.cm-s-obsidian .cm-templater-command.cm-property,
.cm-s-obsidian .cm-templater-command.cm-property.cm-def,
.cm-s-obsidian .cm-templater-command.cm-attribute {
    color: var(--code-function, #98e342);
}

.cm-s-obsidian .cm-templater-command.cm-variable,
.cm-s-obsidian .cm-templater-command.cm-variable-2,
.cm-s-obsidian .cm-templater-command.cm-variable-3,
.cm-s-obsidian .cm-templater-command.cm-meta {
    color: var(--code-property, #d4d4d4);
}

.cm-s-obsidian .cm-templater-command.cm-callee,
.cm-s-obsidian .cm-templater-command.cm-operator,
.cm-s-obsidian .cm-templater-command.cm-qualifier,
.cm-s-obsidian .cm-templater-command.cm-builtin {
    color: var(--code-operator, #fc4384);
}

.cm-s-obsidian .cm-templater-command.cm-tag {
    color: var(--code-tag, #fc4384);
}

.cm-s-obsidian .cm-templater-command.cm-comment,
.cm-s-obsidian .cm-templater-command.cm-comment.cm-tag,
.cm-s-obsidian .cm-templater-command.cm-comment.cm-attribute {
    color: var(--code-comment, #696d70);
}

.cm-s-obsidian .cm-templater-command.cm-string,
.cm-s-obsidian .cm-templater-command.cm-string-2 {
    color: var(--code-string, #e6db74);
}

.cm-s-obsidian .cm-templater-command.cm-header,
.cm-s-obsidian .cm-templater-command.cm-hr {
    color: var(--code-keyword, #da7dae);
}

.cm-s-obsidian .cm-templater-command.cm-link {
    color: var(--code-normal, #696d70);
}

.cm-s-obsidian .cm-templater-command.cm-error {
    border-bottom: 1px solid #c42412;
}

.CodeMirror-hints {
    position: absolute;
    z-index: 10;
    overflow: hidden;
    list-style: none;

    margin: 0;
    padding: 2px;

    -webkit-box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
    box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    border: 1px solid silver;

    background: white;
    font-size: 90%;
    font-family: monospace;

    max-height: 20em;
    overflow-y: auto;
}

.CodeMirror-hint {
    margin: 0;
    padding: 0 4px;
    border-radius: 2px;
    white-space: pre;
    color: black;
    cursor: pointer;
}

li.CodeMirror-hint-active {
    background: #08f;
    color: white;
}
 /* 
 * Styles for Edit History plugin
 */

 /* Show the history modal at modal dimensions, this prevents the dialog
    from growing/shrinking depending on the diff contents */
 .modal.edit-history-modal {
     width: var(--modal-width);
     height: var(--modal-height);
 }

/* Modal displaying the edit history */
.edit-history-modal-content {
    display: flex;
    overflow: hidden;
    flex-direction: column;
}

/* Div displaying the diff inside the edit history modal */
.edit-history-modal-content .diff-div {
    /* Set styles to scroll the div, not the parent modal */
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: auto;
    height: 100%;
    overflow-y: scroll;
    user-select: text;
    /* Preserve spaces/tabs so diffs of those are visible (but still line-wrap
    long text) */
    white-space: pre-wrap;
    /* Wrap anywhere so there's no horizontal scrollbar, especially necessary
    for side by side diffs since they are too wide otherwise */
    overflow-wrap: anywhere;
}

.edit-history-modal-content {
    --edit-history-background-del-rgb: var(--background-modifier-error-rgb);
    --edit-history-background-ins-rgb: var(--background-modifier-success-rgb);
    --edit-history-background-calendar-full-rgb: var(--background-modifier-success-rgb);
    --edit-history-background-calendar-selected-rgb: var(--background-modifier-error-rgb);
    --edit-history-background-calendar-odd: var(--color-base-70);
    --edit-history-background-calendar-even: var(--color-base-60);
}

/* Remove the padding from the default diff-line */
.edit-history-modal-content .diff-div .diff-line {
    /* padding: 0 var(--size-4-2); */
    padding: 0 0;
}
.edit-history-modal-content .diff-div .diff-line .mod-left {
    background-color: rgba(var(--edit-history-background-del-rgb), 0.2);
}
.edit-history-modal-content .diff-div .diff-line .mod-left del {
    background-color: rgba(var(--edit-history-background-del-rgb), 0.4);
}
/* Currently highlighted line (side by side) */
.edit-history-modal-content .diff-div .diff-line.current .mod-left {
    background-color: rgba(var(--edit-history-background-del-rgb), 0.5);
}
/* Currently highlighted diff (non side by side) */
.edit-history-modal-content .diff-div del.diff-line.current  {
    background-color: rgba(var(--edit-history-background-del-rgb), 0.8);
}

.edit-history-modal-content .diff-div .diff-line .mod-right {
    background-color: rgba(var(--edit-history-background-ins-rgb), 0.2);
}
.edit-history-modal-content .diff-div .diff-line .mod-right ins {
    background-color: rgba(var(--edit-history-background-ins-rgb), 0.4);
}
/* Currently highlighted line (side by side) */
.edit-history-modal-content .diff-div .diff-line.current .mod-right  {
    background-color: rgba(var(--edit-history-background-ins-rgb), 0.5);
}
/* Currently highlighted diff (non side by side) */
.edit-history-modal-content .diff-div ins.diff-line.current  {
    background-color: rgba(var(--edit-history-background-ins-rgb), 0.8);
}

/* Calendar headers */
.edit-history-modal-content .calendar th {
    width: 10px;
    line-height: 10px;
    font-size: small;
    font-weight: normal;
}
/* Calendar background for odd months */
.edit-history-modal-content td.calendar-empty-odd {
    background-color: var(--edit-history-background-calendar-odd);
}
/* Calendar background for even months */
.edit-history-modal-content td.calendar-empty-even {
    background-color: var(--edit-history-background-calendar-even);
}
.edit-history-modal-content .calendar td {
    width: 10px;
    line-height: 10px;
    font-size: small;
}
/* Shades of a cell */
/* XXX This is only for unselected cells, have shades for the selected cell too? */
.edit-history-modal-content td.calendar-level.level-0 {
    background-color: rgba(var(--edit-history-background-calendar-full-rgb), 0.2);
}
.edit-history-modal-content td.calendar-level.level-1 {
    background-color: rgba(var(--edit-history-background-calendar-full-rgb), 0.4);
}
.edit-history-modal-content td.calendar-level.level-2 {
    background-color: rgba(var(--edit-history-background-calendar-full-rgb), 0.6);
}
.edit-history-modal-content td.calendar-level.level-3 {
    background-color: rgba(var(--edit-history-background-calendar-full-rgb), 0.8);
}
.edit-history-modal-content td.calendar-level.level-4 {
    background-color: rgba(var(--edit-history-background-calendar-full-rgb), 0.9);
}
.edit-history-modal-content td.calendar-level.level-5 {
    background-color: rgba(var(--edit-history-background-calendar-full-rgb), 1.0);
}
.edit-history-modal-content td.calendar-selected {
    background-color: rgba(var(--edit-history-background-calendar-selected-rgb), 1.0);
}
.edit-history-modal-content td.calendar-black {
    /* background-color: #000000; */
}
.edit-history-modal-content .clickable {
    cursor: pointer;
}
/* Time in timeline view */
.edit-history-modal-content td.diff-time {
    white-space: nowrap;
    font-family: 'Courier New', Courier, monospace;
} .omnisearch-modal {
}

.omnisearch-result {
  white-space: normal;
  display: flex;
  flex-direction: row;
  /* justify-content: space-between; */
  flex-wrap: nowrap;
}

.omnisearch-result__title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  column-gap: 5px;
  flex-wrap: wrap;
}

.omnisearch-result__title {
  white-space: pre-wrap;
  align-items: center;
  display: flex;
  gap: 5px;
}

.omnisearch-result__title > span {
}

.omnisearch-result__folder-path {
  font-size: 0.75rem;
  align-items: center;
  display: flex;
  gap: 5px;
  color: var(--text-muted);
}

.omnisearch-result__extension {
  font-size: 0.7rem;
  color: var(--text-muted);
}

.omnisearch-result__counter {
  font-size: 0.7rem;
  color: var(--text-muted);
}

.omnisearch-result__body {
  white-space: normal;
  font-size: small;
  word-wrap: normal;

  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;

  color: var(--text-muted);
  margin-inline-start: 0.5em;
}

.omnisearch-result__embed {
  margin-left: 1em;
}


.omnisearch-result__image-container {
  flex-basis: 20%;
  text-align: end;
}

.omnisearch-highlight {
}

.omnisearch-default-highlight {
  text-decoration: underline;
  text-decoration-color: var(--text-highlight-bg);
  text-decoration-thickness: 3px;
  text-underline-offset: -1px;
  text-decoration-skip-ink: none;
}

.omnisearch-input-container {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 5px;
}

.omnisearch-result__icon {
  display: inline-block;
  vertical-align: middle;
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.omnisearch-result__icon svg {
  width: 100%;
  height: 100%;
}

.omnisearch-result__icon--emoji {
  font-size: 16px;
  vertical-align: middle;
  margin-right: 4px;
}

@media only screen and (max-width: 600px) {
  .omnisearch-input-container {
    flex-direction: column;
  }

  .omnisearch-input-container__buttons {
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 0 1em 0 1em;
    gap: 1em;
  }
  .omnisearch-input-container__buttons > button {
    flex-grow: 1;
  }
}

@media only screen and (min-width: 600px) {
  .omnisearch-input-container__buttons {
    margin-inline-end: 1em;
  }
}

.omnisearch-input-field {
  position: relative;
  flex-grow: 1;
}
 .block-language-dataview {
    overflow-y: auto;
}

/*****************/
/** Table Views **/
/*****************/

/* List View Default Styling; rendered internally as a table. */
.table-view-table {
    width: 100%;
}

.table-view-table > thead > tr, .table-view-table > tbody > tr {
    margin-top: 1em;
    margin-bottom: 1em;
    text-align: left;
}

.table-view-table > tbody > tr:hover {
    background-color: var(--table-row-background-hover);
}

.table-view-table > thead > tr > th {
    font-weight: 700;
    font-size: larger;
    border-top: none;
    border-left: none;
    border-right: none;
    border-bottom: solid;

    max-width: 100%;
}

.table-view-table > tbody > tr > td {
    text-align: left;
    border: none;
    font-weight: 400;
    max-width: 100%;
}

.table-view-table ul, .table-view-table ol {
    margin-block-start: 0.2em !important;
    margin-block-end: 0.2em !important;
}

/** Rendered value styling for any view. */
.dataview-result-list-root-ul {
    padding: 0em !important;
    margin: 0em !important;
}

.dataview-result-list-ul {
    margin-block-start: 0.2em !important;
    margin-block-end: 0.2em !important;
}

/** Generic grouping styling. */
.dataview.result-group {
    padding-left: 8px;
}

/*******************/
/** Inline Fields **/
/*******************/

.dataview.inline-field-key {
    padding-left: 8px;
    padding-right: 8px;
    font-family: var(--font-monospace);
    background-color: var(--background-primary-alt);
    color: var(--nav-item-color-selected);
}

.dataview.inline-field-value {
    padding-left: 8px;
    padding-right: 8px;
    font-family: var(--font-monospace);
    background-color: var(--background-secondary-alt);
    color: var(--nav-item-color-selected);
}

.dataview.inline-field-standalone-value {
    padding-left: 8px;
    padding-right: 8px;
    font-family: var(--font-monospace);
    background-color: var(--background-secondary-alt);
    color: var(--nav-item-color-selected);
}

/***************/
/** Task View **/
/***************/

.dataview.task-list-item, .dataview.task-list-basic-item {
    margin-top: 3px;
    margin-bottom: 3px;
    transition: 0.4s;
}

.dataview.task-list-item:hover, .dataview.task-list-basic-item:hover {
    background-color: var(--text-selection);
    box-shadow: -40px 0 0 var(--text-selection);
    cursor: pointer;
}

/*****************/
/** Error Views **/
/*****************/

div.dataview-error-box {
    width: 100%;
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 4px dashed var(--background-secondary);
}

.dataview-error-message {
    color: var(--text-muted);
    text-align: center;
}

/*************************/
/** Additional Metadata **/
/*************************/

.dataview.small-text {
    font-size: smaller;
    color: var(--text-muted);
    margin-left: 3px;
}

.dataview.small-text::before {
	content: "(";
}

.dataview.small-text::after {
	content: ")";
}
 .linter-navigation-item{align-items:center;background-color:var(--background-primary-secondary-alt);border:1px solid var(--background-modifier-border);border-radius:100px;border-radius:8px 8px 2px 2px;cursor:pointer;display:flex;flex-direction:row;font-size:16px;font-weight:700;gap:4px;height:32px;overflow:hidden;padding:4px 6px;transition:color .25s ease-in-out,padding .25s ease-in-out,background-color .35s cubic-bezier(.45,.25,.83,.67),max-width .35s cubic-bezier(.57,.04,.58,1);white-space:nowrap}@media screen and (max-width:1325px){.linter-navigation-item.linter-desktop{max-width:32px}}@media screen and (max-width:800px){.linter-navigation-item.linter-mobile{max-width:32px}}.linter-navigation-item-icon,.linter-warning{padding-top:5px}.linter-navigation-item:hover{border-color:var(--interactive-accent-hover);border-bottom:0}.linter-navigation-item-selected{background-color:var(--interactive-accent)!important;border:1px solid var(--background-modifier-border);border-bottom:0;border-radius:8px 8px 2px 2px;color:var(--text-on-accent);max-width:100%!important;padding:4px 9px!important;transition:color .25s ease-in-out,padding .25s ease-in-out,background-color .35s cubic-bezier(.45,.25,.83,.67),max-width .45s cubic-bezier(.57,.04,.58,1) .2s}.linter{transition:transform .4s 0s}.linter-setting-title{align-items:baseline;display:flex;gap:30px;justify-content:space-between}.linter-setting-title.linter-mobile{justify-content:space-around}.linter-setting-title h1{font-weight:900;margin-bottom:12px;margin-top:6px}.linter-setting-header{margin-bottom:24px;overflow-x:auto;overflow-y:hidden}.linter-setting-header .linter-setting-tab-group{align-items:flex-end;display:flex;flex-wrap:wrap;width:100%}.linter-setting-tab-group{border-bottom:2px solid var(--background-modifier-border);margin-top:6px;padding-left:2px;padding-right:2px}.linter-setting-header .linter-tab-settings{border-left:2px solid transparent;border-right:2px solid transparent;cursor:pointer;font-weight:600;padding:6px 12px;white-space:nowrap}.linter-setting-header .linter-tab-settings:first-child{margin-left:6px}.linter-setting-header .linter-tab-settings.linter-tab-settings-active{border:2px solid var(--background-modifier-border);border-bottom-color:var(--background-primary);border-radius:2px;transform:translateY(2px)}.linter-navigation-item:not(.linter-navigation-item-selected)>span:nth-child(2),.linter-visually-hidden{border:0;clip:rect(0 0 0 0);clip-path:rect(0 0 0 0);height:auto;margin:0;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}textarea.full-width{margin-bottom:.8em;margin-top:.8em;min-height:10em;width:100%}.full-width-textbox-input-wrapper{position:relative}.settings-copy-button{margin:0 0 0 auto;padding:4px;position:absolute;right:.8em;top:.8em}.settings-copy-button svg.linter-clipboard path{fill:var(--text-faint)}.settings-copy-button svg.linter-success path{fill:var(--interactive-success)}.settings-copy-button:active,.settings-copy-button:hover{cursor:pointer}.settings-copy-button:active svg path,.settings-copy-button:hover svg path{fill:var(--text-accent-hover);transition:all .3s ease}.settings-copy-button:focus{outline:0}.linter-custom-regex-replacement-container div:last-child{border:none}.linter-custom-regex-replacement{border:none;border-bottom:var(--hr-thickness) solid;border-color:var(--hr-color);margin-bottom:15px}.linter-custom-regex-replacement-row2{flex-wrap:wrap}.linter-custom-regex-replacement-normal-input{width:40%}.linter-custom-regex-replacement-flags{width:15%}.linter-custom-regex-replacement-label{flex-direction:row-reverse}.linter-custom-regex-replacement-label-input{width:50%}.linter-files-to-ignore-container div:last-child{border:none}.linter-files-to-ignore{border:none;border-bottom:var(--hr-thickness) solid;border-color:var(--hr-color);margin-bottom:15px}.linter-files-to-ignore-normal-input{width:40%}.linter-files-to-ignore-flags{width:15%}.linter-no-border{border:none}.linter-border-bottom{border-bottom:1px solid var(--background-modifier-border);border-top:0;margin-bottom:.75em}.linter-no-padding-top{padding-top:0}.custom-row-description{margin-top:0}.modal-warn,.search-zero-state{font-weight:700}.modal-heading,.search-zero-state{text-align:center} .configureMacroDiv{display:grid;grid-template-rows:1fr;min-width:12rem}.configureMacroDivItem{display:flex;align-content:center;justify-content:space-between;margin-bottom:10px}.configureMacroDivItemButton{display:flex;align-content:center;justify-content:center;margin-bottom:10px}.macroContainer{display:grid;grid-template-rows:repeat(auto-fill,120px);grid-gap:40px;overflow-y:auto;max-height:30em;padding:2em}@media screen and (max-width: 540px){.macroContainer1,.macroContainer2,.macroContainer3{grid-template-columns:repeat(1,1fr)}.wideInputPromptInputEl{width:20rem;max-width:100%;height:3rem;direction:inherit;text-align:inherit}}@media screen and (max-width: 540px) and (max-width: 780px){.macroContainer1{grid-template-columns:repeat(1,1fr)}.macroContainer2,.macroContainer3{grid-template-columns:repeat(2,1fr)}.wideInputPromptInputEl{width:30rem;max-width:100%;height:20rem;direction:inherit;text-align:inherit}}@media screen and (min-width: 781px){.macroContainer1{grid-template-columns:repeat(1,1fr)}.macroContainer2,.macroContainer3{grid-template-columns:repeat(2,1fr)}.wideInputPromptInputEl{width:40rem;max-width:100%;height:20rem;direction:inherit;text-align:inherit}}.addMacroBarContainer{display:flex;align-content:center;justify-content:space-around;margin-top:20px}.captureToActiveFileContainer{display:flex;align-content:center;justify-content:space-between;margin-bottom:10px}.choiceNameHeader{text-align:center}.choiceNameHeader:hover{cursor:pointer}.folderInputContainer{display:flex;align-content:center;justify-content:space-between;margin-bottom:8px;gap:4px}.selectMacroDropdownContainer{display:flex;align-content:center;justify-content:center}.quickAddModal .modal{min-width:35%;overflow-y:auto;max-height:70%}.checkboxRowContainer{margin:30px 0;display:grid;grid-template-rows:auto;align-content:center;gap:5px}.checkboxRow{display:flex;justify-content:space-between;align-content:center}.checkboxRow .checkbox-container{flex-shrink:0}.checkboxRow span{font-size:16px;word-break:break-all}.submitButtonContainer{display:flex;align-content:center;justify-content:center}.chooseFolderWhenCreatingNoteContainer{display:flex;align-content:center;justify-content:space-between;margin-bottom:10px}.chooseFolderFromSubfolderContainer{margin:20px 0 0}.clickable:hover{cursor:pointer}.quickAddCommandListItem{display:flex;flex:1 1 auto;align-items:center;justify-content:space-between}.quickCommandContainer{display:flex;justify-content:flex-end;align-content:center;margin-bottom:1em;gap:4px}.yesNoPromptButtonContainer{display:flex;align-items:center;justify-content:space-around;margin-top:2rem}.yesNoPromptParagraph{text-align:center}.suggestion-container{background-color:var(--modal-background);z-index:100000;overflow-y:auto}.qaFileSuggestionItem{display:flex;flex-direction:column;width:100%}.qaFileSuggestionItem .suggestion-main-text{font-weight:700}.qaFileSuggestionItem .suggestion-sub-text{font-style:italic}.choiceListItem{display:flex;font-size:16px;align-items:center;margin:12px 0 0;transition:1s ease-in-out}.choiceListItemName{flex:1 0 0}.choiceListItemName p{margin:0;display:inline}.quickadd-choice-suggestion p{margin:0}.macroDropdownContainer{display:flex;align-content:center;justify-content:center;margin-bottom:10px;gap:10px}.macro-choice-buttonsContainer{display:flex;flex-direction:row;justify-content:center;align-items:center;gap:10px}@media only screen and (max-width: 600px){.macroDropdownContainer{flex-direction:column;align-items:center}.macroDropdownContainer .macro-choice-buttonsContainer{gap:20px}}.quickadd-update-modal-container{display:flex;flex-direction:column;align-items:center;justify-content:center}.quickadd-update-modal{min-width:35%;max-height:70%}.quickadd-update-modal img{width:100%;height:auto;margin:5px}.quickadd-bmac-container{display:flex;justify-content:center;align-items:center}
 .nv-homepage-interstitial {
	position: absolute;
	left: 0;
	top: 0;
	width: 100vw;
	height: 100vh;
	background: var(--background-primary);
	z-index: 9999;
	animation: 0.02s ease-in 0.5s forwards nv-interstitial-destroy;
	pointer-events: none;
}

@keyframes nv-interstitial-destroy {
  from { opacity: 1; }
  to { opacity: 0; }
}

.setting-item[nv-greyed] {
	opacity: .5; 
	pointer-events: none !important;
}

#nv-main-setting {
	flex-wrap: wrap;
	margin-bottom: 30px;
}

#nv-main-setting .setting-item-control {
	padding-top: var(--size-4-2);
	flex-basis: 100%;
	align-items: stretch;
}

#nv-main-setting .setting-item-control input, #nv-main-setting .setting-item-control select {
	font-size: var(--font-ui-medium);
	font-weight: 600;
}

#nv-main-setting .setting-item-control select {
	padding: var(--size-4-3) var(--size-4-4);
	padding-right: var(--size-4-8);
	height: auto;
}

#nv-main-setting .setting-item-control input {
	flex-grow: 1;
	padding: var(--size-4-5) var(--size-4-4);
}

#nv-main-setting .setting-item-control input[disabled] {
	opacity: 0.3;
}

#nv-main-setting #nv-desc, #nv-main-setting #nv-info {
	flex-basis: 100%;
}

#nv-main-setting #nv-desc {
	font-weight: 500;
	color: var(--text-normal);
	font-size: var(--font-ui-small);
	padding: 10px 0 0;
}

#nv-main-setting #nv-desc.mod-warning {
	color: var(--text-error);
}

#nv-main-setting #nv-desc code {
	font-family: var(--font-monospace);
	font-size: var(--font-smaller);
	border-radius: var(--radius-s);
}

#nv-main-setting #nv-desc small {
	display: block;
	font-weight: 400;
	color: var(--text-muted);
	font-size: calc(var(--font-ui-smaller) * 0.9);
	padding: 5px 0 0;
}

.nv-homepage-file-tag {
	display: inline-block;
	vertical-align: middle;
	margin-left: var(--size-2-2);
}

.nv-mobile-setting {
	flex-wrap: wrap;
	row-gap: var(--size-2-2);
}

.nv-mobile-setting .nv-mobile-info {
	font-size: var(--font-ui-smaller);
	width: 100%;
	margin-right: var(--size-4-18);
}

.nv-command-desc {
	padding: 1.2em 0 0;
	border-top: 1px solid var(--background-modifier-border);
}

.nv-command-box {
	margin: 1em 0 1.75em;
	display: flex;
	flex-wrap: wrap;
	gap: 12px;
	align-items: center;
}

.nv-command-pill {
	background-color: var(--background-secondary);
	border: 1px solid var(--background-modifier-border-hover);
	border-radius: var(--radius-s);
	font-size: var(--font-ui-small);
	padding: var(--size-2-1) var(--size-2-2) var(--size-2-1) var(--size-2-3) ;
}

.nv-command-pill.nv-command-invalid {
	color: var(--text-faint);
}

.nv-command-pill button {
	display: inline-block;
	padding: 0;
	margin: 0 0 0 3px;
	vertical-align: bottom;
}

.nv-command-pill button:first-of-type {
	margin-left: var(--size-4-2);
}

.nv-command-pill button.nv-command-selected {
	margin-left: var(--size-2-2);
	padding: 0 var(--size-2-1);
}

.nv-command-pill button.nv-command-selected span {
	color: var(--text-accent);
	display: inline-block;
	font-size: 0.9em;
	vertical-align: top;
	position: relative;
	top: -1px;
}

.nv-command-pill > .svg-icon, .nv-command-pill button .svg-icon {
	height: 1em;
	width: 1em;
}

.nv-command-pill > .svg-icon {
	vertical-align: text-bottom;
	position: relative;
	margin: 0 var(--size-2-1) 0 0;
}

.nv-command-pill.nv-dragging {
	background-color: transparent;
}

.nv-command-add-button {
	font-size: var(--font-ui-small);
	padding: var(--size-2-2) var(--size-4-2);
	height: auto;
}

#nv-main-setting + .setting-item, .nv-command-desc + .setting-item {
	padding-top: 20px; 
	border-top: none !important;
}

.nv-debug-button {
	margin: 3em 0 -0.2em;
	font-size: var(--font-ui-smaller);
	padding: 0;
	height: auto;
	float: right;
	box-shadow: none !important;
	background: none !important;
	color: var(--text-accent);
	font-weight: 600;
	cursor: pointer;
}

.nv-debug-button:hover, .nv-debug-button:active {
	text-decoration: underline;
}

.is-phone #nv-main-setting .setting-item-control {
	flex-wrap: wrap;
	justify-content: flex-start;
}

.is-phone #nv-main-setting .setting-item-control select {
	width: auto;
	max-width: auto;
}

.is-phone .nv-mobile-setting {
	row-gap: var(--size-4-2);
}

.is-phone .nv-mobile-setting .setting-item-info {
	max-width: calc(100% - 100px);
}

.is-phone .nv-mobile-setting {
	row-gap: var(--size-4-2);
}

.is-phone .nv-mobile-setting .setting-item-info {
	max-width: calc(100% - 100px);
}

.is-phone .nv-command-pill {
	width: 100%;
	border: none;
	background: none;
	padding: 0 0 var(--size-4-2);
	display: flex;
	gap: var(--size-4-4);
	align-items: baseline;
}

.is-phone .nv-command-pill .nv-command-text {
	flex-grow: 1;
	overflow: hidden;
	text-overflow: ellipsis;
}

.is-phone .nv-command-pill, .is-phone .nv-command-add-button {
	font-size: var(--font-ui-medium);
	justify-content: space-between;
}

.is-phone .nv-command-pill button {
	line-height: var(--font-ui-medium);
	height: 100%;
	margin: 0 !important;
}
 .style-settings-heading {
    cursor: pointer;
    margin-bottom: 18px;
    padding-bottom: 6px;
    border-bottom: 1px solid var(--background-modifier-border);
}

.style-settings-heading[data-level="0"] {
    margin-bottom: 26px;
}

.style-settings-container {
    padding-bottom: 16px;
}

.style-settings-heading[data-level="0"] + .style-settings-container {
    padding-left: 34px;
}

.style-settings-heading.is-collapsed {
    margin-bottom: 0;
}

.style-settings-heading.is-collapsed + .style-settings-container {
    display: none;
}

.style-settings-collapse-indicator {
    color: var(--text-faint);
    display: inline-block;
    margin-right: 8px;
    position: relative;
    top: -1px;
}

.style-settings-heading[data-level="0"]
+ .style-settings-container
.style-settings-collapse-indicator {
    margin-left: -17px;
}

.style-settings-collapse-indicator > svg {
    height: 9px;
    width: 9px;
}

.style-settings-heading.is-collapsed .style-settings-collapse-indicator > svg {
    transform: rotate(-90deg);
}

.style-settings-filter-result-count {
    color: var(--text-faint);
    line-height: var(--line-height-tight);
    margin-inline: var(--size-4-2);
}

.style-settings-error {
    font-size: 14px;
    border-radius: 6px;
    background: rgba(var(--background-modifier-error-rgb), 0.2);
    color: var(--text-error);
    padding: 10px;
    margin-bottom: 1rem;
}

.style-settings-error-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.style-settings-error-desc {
    white-space: pre;
}

.style-settings-empty {
    font-size: 14px;
    background: var(--background-secondary);
    padding: 10px;
}

.style-settings-empty-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.style-settings-import-input {
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    z-index: -1;
}

.style-settings-import-label {
    cursor: pointer;
    color: var(--text-accent);
    text-decoration: underline;
}

.style-settings-import-label:hover {
    color: var(--text-accent-hover);
}

.style-settings-export,
.style-settings-import {
    display: inline-block;
    margin-right: 10px;
}

.style-settings-copy,
.style-settings-download {
    position: relative;
    display: inline-block;
    margin-left: 10px;
}

.style-settings-copy:before {
    color: var(--interactive-success);
    content: "✓";
    position: absolute;
    left: -18px;
    font-weight: bold;
    opacity: 0;
    transition: 150ms opacity ease-in-out;
}

.style-settings-copy.success:before {
    opacity: 1;
}

.modal-style-settings {
    height: 70vh;
    display: flex;
    flex-direction: column;
}

.modal-style-settings .modal-content {
    flex-grow: 1;
    margin: 0;
    display: flex;
    flex-direction: column;
}

.modal-style-settings textarea {
    display: block;
    width: 100%;
    height: 100%;
    font-family: var(--font-monospace) !important;
    font-size: 12px;
    white-space: pre;
    overflow-wrap: normal;
    overflow-x: scroll;
    margin-bottom: 5px;
}

.modal-style-settings .setting-item {
    align-items: flex-start;
}

.modal-style-settings button {
    margin: 10px 0 0;
}

.style-settings-import-error {
    display: none;
    color: var(--text-error);
}

.style-settings-import-error.active {
    display: block;
}

.view-content .style-settings-container .setting-item:not(.setting-item-heading) {
    flex-direction: column;
    align-items: flex-start;
}

.view-content .style-settings-container .setting-item:not(.setting-item-heading) .setting-item-control {
    padding-top: 0.5em;
}

.view-content .style-settings-container .setting-item:not(.setting-item-heading) .themed-color-wrapper {
    display: flex;
}

.style-settings-ref {
    position: absolute;
    width: 0 !important;
    height: 0 !important;
    pointer-events: none;
}

.style-settings-info-text .style-settings-markdown :first-child {
    margin-top: 0;
}

.style-settings-info-text .style-settings-markdown :last-child {
    margin-bottom: 0;
}.style-settings-container .pcr-app {
    display: none;
}

.style-settings-container .pcr-app.visible {
    display: flex;
}

.pcr-app .pcr-swatches > button {
    padding: 0;
}

.pickr .pcr-button {
    margin-right: 0;
}

.themed-color-wrapper > div {
    background: var(--background-primary);
    padding: 10px;
    display: flex;
    align-items: center;
    border-radius: 4px;
}

.themed-color-wrapper > div + div {
    margin-top: 6px;
}

.themed-color-wrapper button {
    display: block;
}

.themed-color-wrapper .pickr-reset > button {
    margin: 0 0 0 10px;
    padding: 9px;
    line-height: 1;
}

.themed-color-wrapper .pickr-reset > button > svg {
    display: block;
}
/*! Pickr 1.8.4 MIT | https://github.com/Simonwep/pickr */
.pickr{position:relative;overflow:visible;transform:translateY(0)}.pickr *{box-sizing:border-box;outline:none;border:none;-webkit-appearance:none}.pickr .pcr-button{position:relative;height:2em;width:2em;padding:0.5em;cursor:pointer;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;border-radius:.15em;background:url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50" stroke="%2342445A" stroke-width="5px" stroke-linecap="round"><path d="M45,45L5,5"></path><path d="M45,5L5,45"></path></svg>') no-repeat center;background-size:0;transition:all 0.3s}.pickr .pcr-button::before{position:absolute;content:'';top:0;left:0;width:100%;height:100%;background:url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');background-size:.5em;border-radius:.15em;z-index:-1}.pickr .pcr-button::before{z-index:initial}.pickr .pcr-button::after{position:absolute;content:'';top:0;left:0;height:100%;width:100%;transition:background 0.3s;background:var(--pcr-color);border-radius:.15em}.pickr .pcr-button.clear{background-size:70%}.pickr .pcr-button.clear::before{opacity:0}.pickr .pcr-button.clear:focus{box-shadow:0 0 0 1px rgba(255,255,255,0.85),0 0 0 3px var(--pcr-color)}.pickr .pcr-button.disabled{cursor:not-allowed}.pickr *,.pcr-app *{box-sizing:border-box;outline:none;border:none;-webkit-appearance:none}.pickr input:focus,.pickr input.pcr-active,.pickr button:focus,.pickr button.pcr-active,.pcr-app input:focus,.pcr-app input.pcr-active,.pcr-app button:focus,.pcr-app button.pcr-active{box-shadow:0 0 0 1px rgba(255,255,255,0.85),0 0 0 3px var(--pcr-color)}.pickr .pcr-palette,.pickr .pcr-slider,.pcr-app .pcr-palette,.pcr-app .pcr-slider{transition:box-shadow 0.3s}.pickr .pcr-palette:focus,.pickr .pcr-slider:focus,.pcr-app .pcr-palette:focus,.pcr-app .pcr-slider:focus{box-shadow:0 0 0 1px rgba(255,255,255,0.85),0 0 0 3px rgba(0,0,0,0.25)}.pcr-app{position:fixed;display:flex;flex-direction:column;z-index:10000;border-radius:0.1em;background:#fff;opacity:0;visibility:hidden;transition:opacity 0.3s, visibility 0s 0.3s;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;box-shadow:0 0.15em 1.5em 0 rgba(0,0,0,0.1),0 0 1em 0 rgba(0,0,0,0.03);left:0;top:0}.pcr-app.visible{transition:opacity 0.3s;visibility:visible;opacity:1}.pcr-app .pcr-swatches{display:flex;flex-wrap:wrap;margin-top:0.75em}.pcr-app .pcr-swatches.pcr-last{margin:0}@supports (display: grid){.pcr-app .pcr-swatches{display:grid;align-items:center;grid-template-columns:repeat(auto-fit, 1.75em)}}.pcr-app .pcr-swatches>button{font-size:1em;position:relative;width:calc(1.75em - 5px);height:calc(1.75em - 5px);border-radius:0.15em;cursor:pointer;margin:2.5px;flex-shrink:0;justify-self:center;transition:all 0.15s;overflow:hidden;background:transparent;z-index:1}.pcr-app .pcr-swatches>button::before{position:absolute;content:'';top:0;left:0;width:100%;height:100%;background:url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');background-size:6px;border-radius:.15em;z-index:-1}.pcr-app .pcr-swatches>button::after{content:'';position:absolute;top:0;left:0;width:100%;height:100%;background:var(--pcr-color);border:1px solid rgba(0,0,0,0.05);border-radius:0.15em;box-sizing:border-box}.pcr-app .pcr-swatches>button:hover{filter:brightness(1.05)}.pcr-app .pcr-swatches>button:not(.pcr-active){box-shadow:none}.pcr-app .pcr-interaction{display:flex;flex-wrap:wrap;align-items:center;margin:0 -0.2em 0 -0.2em}.pcr-app .pcr-interaction>*{margin:0 0.2em}.pcr-app .pcr-interaction input{letter-spacing:0.07em;font-size:0.75em;text-align:center;cursor:pointer;color:#75797e;background:#f1f3f4;border-radius:.15em;transition:all 0.15s;padding:0.45em 0.5em;margin-top:0.75em}.pcr-app .pcr-interaction input:hover{filter:brightness(0.975)}.pcr-app .pcr-interaction input:focus{box-shadow:0 0 0 1px rgba(255,255,255,0.85),0 0 0 3px rgba(66,133,244,0.75)}.pcr-app .pcr-interaction .pcr-result{color:#75797e;text-align:left;flex:1 1 8em;min-width:8em;transition:all 0.2s;border-radius:.15em;background:#f1f3f4;cursor:text}.pcr-app .pcr-interaction .pcr-result::-moz-selection{background:#4285f4;color:#fff}.pcr-app .pcr-interaction .pcr-result::selection{background:#4285f4;color:#fff}.pcr-app .pcr-interaction .pcr-type.active{color:#fff;background:#4285f4}.pcr-app .pcr-interaction .pcr-save,.pcr-app .pcr-interaction .pcr-cancel,.pcr-app .pcr-interaction .pcr-clear{color:#fff;width:auto}.pcr-app .pcr-interaction .pcr-save,.pcr-app .pcr-interaction .pcr-cancel,.pcr-app .pcr-interaction .pcr-clear{color:#fff}.pcr-app .pcr-interaction .pcr-save:hover,.pcr-app .pcr-interaction .pcr-cancel:hover,.pcr-app .pcr-interaction .pcr-clear:hover{filter:brightness(0.925)}.pcr-app .pcr-interaction .pcr-save{background:#4285f4}.pcr-app .pcr-interaction .pcr-clear,.pcr-app .pcr-interaction .pcr-cancel{background:#f44250}.pcr-app .pcr-interaction .pcr-clear:focus,.pcr-app .pcr-interaction .pcr-cancel:focus{box-shadow:0 0 0 1px rgba(255,255,255,0.85),0 0 0 3px rgba(244,66,80,0.75)}.pcr-app .pcr-selection .pcr-picker{position:absolute;height:18px;width:18px;border:2px solid #fff;border-radius:100%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.pcr-app .pcr-selection .pcr-color-palette,.pcr-app .pcr-selection .pcr-color-chooser,.pcr-app .pcr-selection .pcr-color-opacity{position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:flex;flex-direction:column;cursor:grab;cursor:-webkit-grab}.pcr-app .pcr-selection .pcr-color-palette:active,.pcr-app .pcr-selection .pcr-color-chooser:active,.pcr-app .pcr-selection .pcr-color-opacity:active{cursor:grabbing;cursor:-webkit-grabbing}.pcr-app[data-theme='nano']{width:14.25em;max-width:95vw}.pcr-app[data-theme='nano'] .pcr-swatches{margin-top:.6em;padding:0 .6em}.pcr-app[data-theme='nano'] .pcr-interaction{padding:0 .6em .6em .6em}.pcr-app[data-theme='nano'] .pcr-selection{display:grid;grid-gap:.6em;grid-template-columns:1fr 4fr;grid-template-rows:5fr auto auto;align-items:center;height:10.5em;width:100%;align-self:flex-start}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-preview{grid-area:2 / 1 / 4 / 1;height:100%;width:100%;display:flex;flex-direction:row;justify-content:center;margin-left:.6em}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-preview .pcr-last-color{display:none}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-preview .pcr-current-color{position:relative;background:var(--pcr-color);width:2em;height:2em;border-radius:50em;overflow:hidden}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-preview .pcr-current-color::before{position:absolute;content:'';top:0;left:0;width:100%;height:100%;background:url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');background-size:.5em;border-radius:.15em;z-index:-1}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-palette{grid-area:1 / 1 / 2 / 3;width:100%;height:100%;z-index:1}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-palette .pcr-palette{border-radius:.15em;width:100%;height:100%}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-palette .pcr-palette::before{position:absolute;content:'';top:0;left:0;width:100%;height:100%;background:url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');background-size:.5em;border-radius:.15em;z-index:-1}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-chooser{grid-area:2 / 2 / 2 / 2}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-opacity{grid-area:3 / 2 / 3 / 2}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-chooser,.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-opacity{height:0.5em;margin:0 .6em}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-chooser .pcr-picker,.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-opacity .pcr-picker{top:50%;transform:translateY(-50%)}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-chooser .pcr-slider,.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-opacity .pcr-slider{flex-grow:1;border-radius:50em}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-chooser .pcr-slider{background:linear-gradient(to right, red, #ff0, lime, cyan, blue, #f0f, red)}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-opacity .pcr-slider{background:linear-gradient(to right, transparent, black),url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');background-size:100%, 0.25em}

 @charset "UTF-8";.flatpickr-calendar{background:transparent;opacity:0;display:none;text-align:center;visibility:hidden;padding:0;-webkit-animation:none;animation:none;direction:ltr;border:0;font-size:14px;line-height:24px;border-radius:5px;position:absolute;width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-touch-action:manipulation;touch-action:manipulation;background:#fff;-webkit-box-shadow:1px 0 0 #e6e6e6,-1px 0 0 #e6e6e6,0 1px 0 #e6e6e6,0 -1px 0 #e6e6e6,0 3px 13px rgba(0,0,0,.08);box-shadow:1px 0 #e6e6e6,-1px 0 #e6e6e6,0 1px #e6e6e6,0 -1px #e6e6e6,0 3px 13px #00000014}.flatpickr-calendar.open,.flatpickr-calendar.inline{opacity:1;max-height:640px;visibility:visible}.flatpickr-calendar.open{display:inline-block;z-index:99999}.flatpickr-calendar.animate.open{-webkit-animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1);animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1)}.flatpickr-calendar.inline{display:block;position:relative;top:2px}.flatpickr-calendar.static{position:absolute;top:calc(100% + 2px)}.flatpickr-calendar.static.open{z-index:999;display:block}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7){-webkit-box-shadow:none!important;box-shadow:none!important}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1){-webkit-box-shadow:-2px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-2px 0 #e6e6e6,5px 0 #e6e6e6}.flatpickr-calendar .hasWeeks .dayContainer,.flatpickr-calendar .hasTime .dayContainer{border-bottom:0;border-bottom-right-radius:0;border-bottom-left-radius:0}.flatpickr-calendar .hasWeeks .dayContainer{border-left:0}.flatpickr-calendar.hasTime .flatpickr-time{height:40px;border-top:1px solid #e6e6e6}.flatpickr-calendar.noCalendar.hasTime .flatpickr-time{height:auto}.flatpickr-calendar:before,.flatpickr-calendar:after{position:absolute;display:block;pointer-events:none;border:solid transparent;content:"";height:0;width:0;left:22px}.flatpickr-calendar.rightMost:before,.flatpickr-calendar.arrowRight:before,.flatpickr-calendar.rightMost:after,.flatpickr-calendar.arrowRight:after{left:auto;right:22px}.flatpickr-calendar.arrowCenter:before,.flatpickr-calendar.arrowCenter:after{left:50%;right:50%}.flatpickr-calendar:before{border-width:5px;margin:0 -5px}.flatpickr-calendar:after{border-width:4px;margin:0 -4px}.flatpickr-calendar.arrowTop:before,.flatpickr-calendar.arrowTop:after{bottom:100%}.flatpickr-calendar.arrowTop:before{border-bottom-color:#e6e6e6}.flatpickr-calendar.arrowTop:after{border-bottom-color:#fff}.flatpickr-calendar.arrowBottom:before,.flatpickr-calendar.arrowBottom:after{top:100%}.flatpickr-calendar.arrowBottom:before{border-top-color:#e6e6e6}.flatpickr-calendar.arrowBottom:after{border-top-color:#fff}.flatpickr-calendar:focus{outline:0}.flatpickr-wrapper{position:relative;display:inline-block}.flatpickr-months{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-months .flatpickr-month{background:transparent;color:#000000e6;fill:#000000e6;height:34px;line-height:1;text-align:center;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;overflow:hidden;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.flatpickr-months .flatpickr-prev-month,.flatpickr-months .flatpickr-next-month{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;text-decoration:none;cursor:pointer;position:absolute;top:0;height:34px;padding:10px;z-index:3;color:#000000e6;fill:#000000e6}.flatpickr-months .flatpickr-prev-month.flatpickr-disabled,.flatpickr-months .flatpickr-next-month.flatpickr-disabled{display:none}.flatpickr-months .flatpickr-prev-month i,.flatpickr-months .flatpickr-next-month i{position:relative}.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,.flatpickr-months .flatpickr-next-month.flatpickr-prev-month{left:0}.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,.flatpickr-months .flatpickr-next-month.flatpickr-next-month{right:0}.flatpickr-months .flatpickr-prev-month:hover,.flatpickr-months .flatpickr-next-month:hover{color:#959ea9}.flatpickr-months .flatpickr-prev-month:hover svg,.flatpickr-months .flatpickr-next-month:hover svg{fill:#f64747}.flatpickr-months .flatpickr-prev-month svg,.flatpickr-months .flatpickr-next-month svg{width:14px;height:14px}.flatpickr-months .flatpickr-prev-month svg path,.flatpickr-months .flatpickr-next-month svg path{-webkit-transition:fill .1s;transition:fill .1s;fill:inherit}.numInputWrapper{position:relative;height:auto}.numInputWrapper input,.numInputWrapper span{display:inline-block}.numInputWrapper input{width:100%}.numInputWrapper input::-ms-clear{display:none}.numInputWrapper input::-webkit-outer-spin-button,.numInputWrapper input::-webkit-inner-spin-button{margin:0;-webkit-appearance:none}.numInputWrapper span{position:absolute;right:0;width:14px;padding:0 4px 0 2px;height:50%;line-height:50%;opacity:0;cursor:pointer;border:1px solid rgba(57,57,57,.15);-webkit-box-sizing:border-box;box-sizing:border-box}.numInputWrapper span:hover{background:rgba(0,0,0,.1)}.numInputWrapper span:active{background:rgba(0,0,0,.2)}.numInputWrapper span:after{display:block;content:"";position:absolute}.numInputWrapper span.arrowUp{top:0;border-bottom:0}.numInputWrapper span.arrowUp:after{border-left:4px solid transparent;border-right:4px solid transparent;border-bottom:4px solid rgba(57,57,57,.6);top:26%}.numInputWrapper span.arrowDown{top:50%}.numInputWrapper span.arrowDown:after{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(57,57,57,.6);top:40%}.numInputWrapper span svg{width:inherit;height:auto}.numInputWrapper span svg path{fill:#00000080}.numInputWrapper:hover{background:rgba(0,0,0,.05)}.numInputWrapper:hover span{opacity:1}.flatpickr-current-month{font-size:135%;line-height:inherit;font-weight:300;color:inherit;position:absolute;width:75%;left:12.5%;padding:7.48px 0 0;line-height:1;height:34px;display:inline-block;text-align:center;-webkit-transform:translate3d(0px,0px,0px);transform:translateZ(0)}.flatpickr-current-month span.cur-month{font-family:inherit;font-weight:700;color:inherit;display:inline-block;margin-left:.5ch;padding:0}.flatpickr-current-month span.cur-month:hover{background:rgba(0,0,0,.05)}.flatpickr-current-month .numInputWrapper{width:6ch;width:7ch\fffd;display:inline-block}.flatpickr-current-month .numInputWrapper span.arrowUp:after{border-bottom-color:#000000e6}.flatpickr-current-month .numInputWrapper span.arrowDown:after{border-top-color:#000000e6}.flatpickr-current-month input.cur-year{background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;color:inherit;cursor:text;padding:0 0 0 .5ch;margin:0;display:inline-block;font-size:inherit;font-family:inherit;font-weight:300;line-height:inherit;height:auto;border:0;border-radius:0;vertical-align:initial;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-current-month input.cur-year:focus{outline:0}.flatpickr-current-month input.cur-year[disabled],.flatpickr-current-month input.cur-year[disabled]:hover{font-size:100%;color:#00000080;background:transparent;pointer-events:none}.flatpickr-current-month .flatpickr-monthDropdown-months{appearance:menulist;background:transparent;border:none;border-radius:0;box-sizing:border-box;color:inherit;cursor:pointer;font-size:inherit;font-family:inherit;font-weight:300;height:auto;line-height:inherit;margin:-1px 0 0;outline:none;padding:0 0 0 .5ch;position:relative;vertical-align:initial;-webkit-box-sizing:border-box;-webkit-appearance:menulist;-moz-appearance:menulist;width:auto}.flatpickr-current-month .flatpickr-monthDropdown-months:focus,.flatpickr-current-month .flatpickr-monthDropdown-months:active{outline:none}.flatpickr-current-month .flatpickr-monthDropdown-months:hover{background:rgba(0,0,0,.05)}.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month{background-color:transparent;outline:none;padding:0}.flatpickr-weekdays{background:transparent;text-align:center;overflow:hidden;width:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:28px}.flatpickr-weekdays .flatpickr-weekdaycontainer{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}span.flatpickr-weekday{cursor:default;font-size:90%;background:transparent;color:#0000008a;line-height:1;margin:0;text-align:center;display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;font-weight:bolder}.dayContainer,.flatpickr-weeks{padding:1px 0 0}.flatpickr-days{position:relative;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start;width:307.875px}.flatpickr-days:focus{outline:0}.dayContainer{padding:0;outline:0;text-align:left;width:307.875px;min-width:307.875px;max-width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;display:inline-block;display:-ms-flexbox;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-wrap:wrap;-ms-flex-pack:justify;-webkit-justify-content:space-around;justify-content:space-around;-webkit-transform:translate3d(0px,0px,0px);transform:translateZ(0);opacity:1}.dayContainer+.dayContainer{-webkit-box-shadow:-1px 0 0 #e6e6e6;box-shadow:-1px 0 #e6e6e6}.flatpickr-day{background:none;border:1px solid transparent;border-radius:150px;-webkit-box-sizing:border-box;box-sizing:border-box;color:#393939;cursor:pointer;font-weight:400;width:14.2857143%;-webkit-flex-basis:14.2857143%;-ms-flex-preferred-size:14.2857143%;flex-basis:14.2857143%;max-width:39px;height:39px;line-height:39px;margin:0;display:inline-block;position:relative;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;text-align:center}.flatpickr-day.inRange,.flatpickr-day.prevMonthDay.inRange,.flatpickr-day.nextMonthDay.inRange,.flatpickr-day.today.inRange,.flatpickr-day.prevMonthDay.today.inRange,.flatpickr-day.nextMonthDay.today.inRange,.flatpickr-day:hover,.flatpickr-day.prevMonthDay:hover,.flatpickr-day.nextMonthDay:hover,.flatpickr-day:focus,.flatpickr-day.prevMonthDay:focus,.flatpickr-day.nextMonthDay:focus{cursor:pointer;outline:0;background:#e6e6e6;border-color:#e6e6e6}.flatpickr-day.today{border-color:#959ea9}.flatpickr-day.today:hover,.flatpickr-day.today:focus{border-color:#959ea9;background:#959ea9;color:#fff}.flatpickr-day.selected,.flatpickr-day.startRange,.flatpickr-day.endRange,.flatpickr-day.selected.inRange,.flatpickr-day.startRange.inRange,.flatpickr-day.endRange.inRange,.flatpickr-day.selected:focus,.flatpickr-day.startRange:focus,.flatpickr-day.endRange:focus,.flatpickr-day.selected:hover,.flatpickr-day.startRange:hover,.flatpickr-day.endRange:hover,.flatpickr-day.selected.prevMonthDay,.flatpickr-day.startRange.prevMonthDay,.flatpickr-day.endRange.prevMonthDay,.flatpickr-day.selected.nextMonthDay,.flatpickr-day.startRange.nextMonthDay,.flatpickr-day.endRange.nextMonthDay{background:#569ff7;-webkit-box-shadow:none;box-shadow:none;color:#fff;border-color:#569ff7}.flatpickr-day.selected.startRange,.flatpickr-day.startRange.startRange,.flatpickr-day.endRange.startRange{border-radius:50px 0 0 50px}.flatpickr-day.selected.endRange,.flatpickr-day.startRange.endRange,.flatpickr-day.endRange.endRange{border-radius:0 50px 50px 0}.flatpickr-day.selected.startRange+.endRange:not(:nth-child(7n+1)),.flatpickr-day.startRange.startRange+.endRange:not(:nth-child(7n+1)),.flatpickr-day.endRange.startRange+.endRange:not(:nth-child(7n+1)){-webkit-box-shadow:-10px 0 0 #569ff7;box-shadow:-10px 0 #569ff7}.flatpickr-day.selected.startRange.endRange,.flatpickr-day.startRange.startRange.endRange,.flatpickr-day.endRange.startRange.endRange{border-radius:50px}.flatpickr-day.inRange{border-radius:0;-webkit-box-shadow:-5px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-5px 0 #e6e6e6,5px 0 #e6e6e6}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover,.flatpickr-day.prevMonthDay,.flatpickr-day.nextMonthDay,.flatpickr-day.notAllowed,.flatpickr-day.notAllowed.prevMonthDay,.flatpickr-day.notAllowed.nextMonthDay{color:#3939394d;background:transparent;border-color:transparent;cursor:default}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover{cursor:not-allowed;color:#3939391a}.flatpickr-day.week.selected{border-radius:0;-webkit-box-shadow:-5px 0 0 #569ff7,5px 0 0 #569ff7;box-shadow:-5px 0 #569ff7,5px 0 #569ff7}.flatpickr-day.hidden{visibility:hidden}.rangeMode .flatpickr-day{margin-top:1px}.flatpickr-weekwrapper{float:left}.flatpickr-weekwrapper .flatpickr-weeks{padding:0 12px;-webkit-box-shadow:1px 0 0 #e6e6e6;box-shadow:1px 0 #e6e6e6}.flatpickr-weekwrapper .flatpickr-weekday{float:none;width:100%;line-height:28px}.flatpickr-weekwrapper span.flatpickr-day,.flatpickr-weekwrapper span.flatpickr-day:hover{display:block;width:100%;max-width:none;color:#3939394d;background:transparent;cursor:default;border:none}.flatpickr-innerContainer{display:block;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden}.flatpickr-rContainer{display:inline-block;padding:0;-webkit-box-sizing:border-box;box-sizing:border-box}.flatpickr-time{text-align:center;outline:0;display:block;height:0;line-height:40px;max-height:40px;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-time:after{content:"";display:table;clear:both}.flatpickr-time .numInputWrapper{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;width:40%;height:40px;float:left}.flatpickr-time .numInputWrapper span.arrowUp:after{border-bottom-color:#393939}.flatpickr-time .numInputWrapper span.arrowDown:after{border-top-color:#393939}.flatpickr-time.hasSeconds .numInputWrapper{width:26%}.flatpickr-time.time24hr .numInputWrapper{width:49%}.flatpickr-time input{background:transparent;-webkit-box-shadow:none;box-shadow:none;border:0;border-radius:0;text-align:center;margin:0;padding:0;height:inherit;line-height:inherit;color:#393939;font-size:14px;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-time input.flatpickr-hour{font-weight:700}.flatpickr-time input.flatpickr-minute,.flatpickr-time input.flatpickr-second{font-weight:400}.flatpickr-time input:focus{outline:0;border:0}.flatpickr-time .flatpickr-time-separator,.flatpickr-time .flatpickr-am-pm{height:inherit;float:left;line-height:inherit;color:#393939;font-weight:700;width:2%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-align-self:center;-ms-flex-item-align:center;align-self:center}.flatpickr-time .flatpickr-am-pm{outline:0;width:18%;cursor:pointer;text-align:center;font-weight:400}.flatpickr-time input:hover,.flatpickr-time .flatpickr-am-pm:hover,.flatpickr-time input:focus,.flatpickr-time .flatpickr-am-pm:focus{background:#eee}.flatpickr-input[readonly]{cursor:pointer}@-webkit-keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}@keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}:root{--tasks-details-icon: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M8.59 16.58L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.42z'/></svg>")}ul.contains-task-list .task-list-item-checkbox{margin-inline-start:calc(var(--checkbox-size) * -1.5)!important}.plugin-tasks-query-explanation{--code-white-space: pre}.tasks-count{color:var(--text-faint);padding-left:20px}.tooltip.pop-up{animation:pop-up-animation .2s forwards ease-in-out}@keyframes pop-up-animation{0%{opacity:0;transform:translateY(-100%) scale(1)}20%{opacity:.7;transform:translateY(-100%) scale(1.02)}40%{opacity:1;transform:translateY(-100%) scale(1.05)}to{opacity:1;transform:translateY(-100%) scale(1)}}.task-cancelled,.task-created,.task-done,.task-due,.task-scheduled,.task-start{cursor:pointer;user-select:none;-webkit-user-select:none;-webkit-touch-callout:none}.tasks-edit,.tasks-postpone{width:1em;height:1em;vertical-align:middle;margin-left:.33em;cursor:pointer;font-family:var(--font-interface);color:var(--text-accent);user-select:none;-webkit-user-select:none;-webkit-touch-callout:none}a.tasks-edit,a.tasks-postpone{text-decoration:none}.tasks-edit:after{content:"\1f4dd"}.tasks-postpone:after{content:"\23e9"}.tasks-urgency{font-size:var(--font-ui-smaller);font-family:var(--font-interface);padding:2px 6px;border-radius:var(--radius-s);color:var(--text-normal);background-color:var(--background-secondary);margin-left:.5em;line-height:1}.internal-link.internal-link-short-mode{text-decoration:none}.tasks-list-text{position:relative}.tasks-list-text .tooltip{position:absolute;top:0;left:0;white-space:nowrap}.task-list-item-checkbox{cursor:pointer}.tasks-layout-hide-tags .task-description a.tag,.task-list-item .task-block-link{display:none}.tasks-modal section+section{margin-top:6px}.tasks-modal hr{margin:6px 0}.tasks-modal .tasks-modal-error{border:1px solid red!important}.tasks-modal .accesskey{text-decoration:underline;text-underline-offset:1pt}.tasks-modal-description-section textarea{width:100%;min-height:calc(var(--input-height) * 2);resize:vertical;margin-top:8px}.tasks-modal-priority-section{display:grid;grid-template-columns:6em auto auto auto;grid-row-gap:.15em}.tasks-modal-priority-section>label{grid-row-start:1;grid-row-end:3}.tasks-modal-priority-section .task-modal-priority-option-container{white-space:nowrap}.tasks-modal-priority-section .task-modal-priority-option-container input+label{font-size:var(--font-ui-small);border-radius:var(--input-radius);padding:2px 3px}.tasks-modal-priority-section .task-modal-priority-option-container input{accent-color:var(--interactive-accent)}.tasks-modal-priority-section .task-modal-priority-option-container input:focus+label{box-shadow:0 0 0 2px var(--background-modifier-border-focus);border-color:var(--background-modifier-border-focus)}.tasks-modal-priority-section .task-modal-priority-option-container input:checked+label{font-weight:700}.tasks-modal-priority-section .task-modal-priority-option-container input:not(:checked)+label>span:nth-child(4){filter:grayscale(100%) opacity(60%)}.tasks-modal-dates-section{display:grid;grid-template-columns:6em 13em auto;column-gap:.5em;row-gap:5px;align-items:center}.tasks-modal-dates-section label{grid-column:1}.tasks-modal-dates-section .tasks-modal-date-input{min-width:15em}.tasks-modal-dates-section .tasks-modal-date-editor-picker{margin-left:.5em}.tasks-modal-dates-section .tasks-modal-parsed-date{grid-column:3;font-size:var(--font-ui-small)}.tasks-modal-dates-section .future-dates-only{grid-column-start:1;grid-column-end:3}.tasks-modal-dates-section .future-dates-only input{margin-left:.67em;top:2px}.tasks-modal-dates-section .status-editor-status-selector{grid-column:2}.tasks-modal-dependencies-section{display:grid;grid-template-columns:6em auto;column-gap:.5em;row-gap:5px;align-items:center}.tasks-modal-dependencies-section .tasks-modal-dependency-input{grid-column:2;width:100%}.tasks-modal-dependencies-section .results-dependency{grid-column:2}.tasks-modal-button-section{position:sticky;bottom:0;background-color:var(--modal-background);padding-bottom:16px;padding-top:16px;display:grid;grid-template-columns:3fr 1fr;column-gap:.5em}.tasks-modal-button-section button:disabled{pointer-events:none!important;opacity:.3!important}@media (max-width: 649px){.tasks-modal-priority-section{grid-template-columns:6em auto auto}.tasks-modal-priority-section>label{grid-row:1/span 3}}@media (max-width: 499px){.tasks-modal-priority-section{grid-template-columns:4em auto auto}.tasks-modal-dates-section{grid-template-columns:1fr;grid-auto-columns:auto}.tasks-modal-dates-section .tasks-modal-date-input{grid-column:1}.tasks-modal-dates-section .tasks-modal-parsed-date{grid-column:2}.tasks-modal-dates-section .status-editor-status-selector,.tasks-modal-dependencies-section label,.tasks-modal-dependencies-section .results-dependency{grid-column:1}}@media (max-width: 399px){.tasks-modal-dates-section .status-editor-status-selector{grid-column:1}.tasks-modal-dates-section>.tasks-modal-parsed-date{grid-column:1}.tasks-modal-priority-section{grid-template-columns:4em auto}.tasks-modal-priority-section>label{grid-row:1/span 6}.tasks-modal-dependencies-section{grid-template-columns:1fr;grid-auto-columns:auto}}@media (max-width: 259px){.tasks-modal-priority-section{grid-template-columns:1fr}.tasks-modal-priority-section>label{grid-row:1}}.task-dependencies-container{grid-column:2;display:flex;flex-wrap:wrap;gap:8px}.task-dependency{display:inline-flex;background-color:var(--interactive-normal);box-shadow:var(--input-shadow);border-radius:28px;padding:4px 4px 4px 8px}.task-dependency-name{font-size:var(--font-ui-small);max-width:160px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.task-dependency-delete{padding:3px;cursor:pointer;height:inherit;box-shadow:none!important;border-radius:50%}.task-dependency-dropdown{list-style:none;position:absolute;top:0;left:0;padding:4px;margin:0;background-color:var(--background-primary);border:1px;border-radius:6px;border-color:var(--background-modifier-border);border-style:solid;z-index:99;max-height:170px;overflow-y:auto}.task-dependency-dropdown li{padding:5px;margin:2px;border-radius:6px;cursor:pointer;display:flex;justify-content:space-between}.task-dependency-dropdown li .dependency-name{overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.task-dependency-dropdown li .dependency-name-shared{width:60%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.task-dependency-dropdown li .dependency-path{width:40%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-style:italic;text-align:right;color:var(--italic-color)}.task-dependency-dropdown li.selected{background-color:var(--text-selection)}.tasks-setting-important{color:red;font-weight:700}.tasks-settings-is-invalid{color:var(--text-error)!important;background-color:rgba(var(--background-modifier-error-rgb),.2)!important}.tasks-settings .additional{margin:6px 12px}.tasks-settings .additional>.setting-item{border-top:0;padding-top:9px}.tasks-settings details>summary{outline:none;display:block!important;list-style:none!important;list-style-type:none!important;min-height:1rem;border-top-left-radius:.1rem;border-top-right-radius:.1rem;cursor:pointer;position:relative}.tasks-settings details>summary::-webkit-details-marker,.tasks-settings details>summary::marker{display:none!important}.tasks-settings details>summary>.collapser{position:absolute;top:50%;right:8px;transform:translateY(-50%);content:""}.tasks-settings details>summary>.collapser>.handle{transform:rotate(0);transition:transform .25s;background-color:currentColor;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat;-webkit-mask-size:contain;mask-size:contain;-webkit-mask-image:var(--tasks-details-icon);mask-image:var(--tasks-details-icon);width:20px;height:20px}.tasks-settings details[open]>summary>.collapser>.handle{transform:rotate(90deg)}.tasks-nested-settings .setting-item{border:0px;padding-bottom:0}.tasks-nested-settings{padding-bottom:18px}.tasks-nested-settings[open] .setting-item-heading,.tasks-nested-settings:not(details) .setting-item-heading{border-top:0px;border-bottom:1px solid var(--background-modifier-border)}.tasks-settings .row-for-status{margin-top:0;margin-bottom:0}
 :root {
  --advanced-tables-helper-size: 28px;
}

.HyperMD-table-row span.cm-inline-code {
  font-size: 100%;
  padding: 0px;
}

.advanced-tables-buttons>div>.title {
  font-weight: var(--font-medium);
  font-size: var(--nav-item-size);
  color: var(--nav-item-color);
  text-decoration: underline;
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container {
  column-gap: 0.2rem;
  margin: 0.2rem 0 0.2rem 0;
  justify-content: start;
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container::before {
  min-width: 2.6rem;
  line-height: var(--advanced-tables-helper-size);
  font-size: var(--nav-item-size);
  font-weight: var(--nav-item-weight);
  color: var(--nav-item-color);
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container>* {
  height: var(--advanced-tables-helper-size);
  line-height: var(--advanced-tables-helper-size);
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container .nav-action-button {
  width: var(--advanced-tables-helper-size);
  height: var(--advanced-tables-helper-size);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-s);
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container .nav-action-button:hover {
  background-color: var(--nav-item-background-hover);
  color: var(--nav-item-color-hover);
  font-weight: var(--nav-item-weight-hover);
}

.advanced-tables-row-label {
  width: 50px;
}

.widget-icon {
  width: 20px;
  height: 20px;
  fill: var(--text-muted);
}

.widget-icon:hover {
  fill: var(--text-normal);
}

.advanced-tables-csv-export textarea {
  height: 200px;
  width: 100%;
}

.advanced-tables-donation {
  width: 70%;
  margin: 0 auto;
  text-align: center;
}

.advanced-tables-donate-button {
  margin: 10px;
} .choices{position:relative;margin-bottom:24px;font-size:16px}.choices:focus{outline:none}.choices:last-child{margin-bottom:0}.choices.is-disabled .choices__inner,.choices.is-disabled .choices__input{background-color:#eaeaea;cursor:not-allowed;-webkit-user-select:none;-ms-user-select:none;user-select:none}.choices.is-disabled .choices__item{cursor:not-allowed}.choices [hidden]{display:none!important}.choices[data-type*=select-one]{cursor:pointer}.choices[data-type*=select-one] .choices__inner{padding-bottom:7.5px}.choices[data-type*=select-one] .choices__input{display:block;width:100%;padding:10px;border-bottom:1px solid #dddddd;background-color:#fff;margin:0}.choices[data-type*=select-one] .choices__button{background-image:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMDAwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);padding:0;background-size:8px;position:absolute;top:50%;right:0;margin-top:-10px;margin-right:25px;height:20px;width:20px;border-radius:10em;opacity:.5}.choices[data-type*=select-one] .choices__button:hover,.choices[data-type*=select-one] .choices__button:focus{opacity:1}.choices[data-type*=select-one] .choices__button:focus{box-shadow:0 0 0 2px #00bcd4}.choices[data-type*=select-one] .choices__item[data-value=""] .choices__button{display:none}.choices[data-type*=select-one]:after{content:"";height:0;width:0;border-style:solid;border-color:#333333 transparent transparent transparent;border-width:5px;position:absolute;right:11.5px;top:50%;margin-top:-2.5px;pointer-events:none}.choices[data-type*=select-one].is-open:after{border-color:transparent transparent #333333 transparent;margin-top:-7.5px}.choices[data-type*=select-one][dir=rtl]:after{left:11.5px;right:auto}.choices[data-type*=select-one][dir=rtl] .choices__button{right:auto;left:0;margin-left:25px;margin-right:0}.choices[data-type*=select-multiple] .choices__inner,.choices[data-type*=text] .choices__inner{cursor:text}.choices[data-type*=select-multiple] .choices__button,.choices[data-type*=text] .choices__button{position:relative;display:inline-block;margin:0 -4px 0 8px;padding-left:16px;border-left:1px solid #008fa1;background-image:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjRkZGIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);background-size:8px;width:8px;line-height:1;opacity:.75;border-radius:0}.choices[data-type*=select-multiple] .choices__button:hover,.choices[data-type*=select-multiple] .choices__button:focus,.choices[data-type*=text] .choices__button:hover,.choices[data-type*=text] .choices__button:focus{opacity:1}.choices__inner{display:inline-block;vertical-align:top;width:100%;background-color:#f9f9f9;padding:7.5px 7.5px 3.75px;border:1px solid #dddddd;border-radius:2.5px;font-size:14px;min-height:44px;overflow:hidden}.is-focused .choices__inner,.is-open .choices__inner{border-color:#b7b7b7}.is-open .choices__inner{border-radius:2.5px 2.5px 0 0}.is-flipped.is-open .choices__inner{border-radius:0 0 2.5px 2.5px}.choices__list{margin:0;padding-left:0;list-style:none}.choices__list--single{display:inline-block;padding:4px 16px 4px 4px;width:100%}[dir=rtl] .choices__list--single{padding-right:4px;padding-left:16px}.choices__list--single .choices__item{width:100%}.choices__list--multiple{display:inline}.choices__list--multiple .choices__item{display:inline-block;vertical-align:middle;border-radius:20px;padding:4px 10px;font-size:12px;font-weight:500;margin-right:3.75px;margin-bottom:3.75px;background-color:#00bcd4;border:1px solid #00a5bb;color:#fff;word-break:break-all;box-sizing:border-box}.choices__list--multiple .choices__item[data-deletable]{padding-right:5px}[dir=rtl] .choices__list--multiple .choices__item{margin-right:0;margin-left:3.75px}.choices__list--multiple .choices__item.is-highlighted{background-color:#00a5bb;border:1px solid #008fa1}.is-disabled .choices__list--multiple .choices__item{background-color:#aaa;border:1px solid #919191}.choices__list--dropdown{visibility:hidden;z-index:1;position:absolute;width:100%;background-color:#fff;border:1px solid #dddddd;top:100%;margin-top:-1px;border-bottom-left-radius:2.5px;border-bottom-right-radius:2.5px;overflow:hidden;word-break:break-all;will-change:visibility}.choices__list--dropdown.is-active{visibility:visible}.is-open .choices__list--dropdown{border-color:#b7b7b7}.is-flipped .choices__list--dropdown{top:auto;bottom:100%;margin-top:0;margin-bottom:-1px;border-radius:.25rem .25rem 0 0}.choices__list--dropdown .choices__list{position:relative;max-height:300px;overflow:auto;-webkit-overflow-scrolling:touch;will-change:scroll-position}.choices__list--dropdown .choices__item{position:relative;padding:10px;font-size:14px}[dir=rtl] .choices__list--dropdown .choices__item{text-align:right}@media (min-width: 640px){.choices__list--dropdown .choices__item--selectable{padding-right:100px}.choices__list--dropdown .choices__item--selectable:after{content:attr(data-select-text);font-size:12px;opacity:0;position:absolute;right:10px;top:50%;transform:translateY(-50%)}[dir=rtl] .choices__list--dropdown .choices__item--selectable{text-align:right;padding-left:100px;padding-right:10px}[dir=rtl] .choices__list--dropdown .choices__item--selectable:after{right:auto;left:10px}}.choices__list--dropdown .choices__item--selectable.is-highlighted{background-color:#f2f2f2}.choices__list--dropdown .choices__item--selectable.is-highlighted:after{opacity:.5}.choices__item{cursor:default}.choices__item--selectable{cursor:pointer}.choices__item--disabled{cursor:not-allowed;-webkit-user-select:none;-ms-user-select:none;user-select:none;opacity:.5}.choices__heading{font-weight:600;font-size:12px;padding:10px;border-bottom:1px solid #f7f7f7;color:gray}.choices__button{text-indent:-9999px;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:0;background-color:transparent;background-repeat:no-repeat;background-position:center;cursor:pointer}.choices__button:focus{outline:none}.choices__input{display:inline-block;vertical-align:baseline;background-color:#f9f9f9;font-size:14px;margin-bottom:5px;border:0;border-radius:0;max-width:100%;padding:4px 0 4px 2px}.choices__input:focus{outline:0}[dir=rtl] .choices__input{padding-right:2px;padding-left:0}.choices__placeholder{opacity:.5}.flatpickr-calendar{background:transparent;opacity:0;display:none;text-align:center;visibility:hidden;padding:0;-webkit-animation:none;animation:none;direction:ltr;border:0;font-size:14px;line-height:24px;border-radius:5px;position:absolute;width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-touch-action:manipulation;touch-action:manipulation;background:#fff;-webkit-box-shadow:1px 0 0 #e6e6e6,-1px 0 0 #e6e6e6,0 1px 0 #e6e6e6,0 -1px 0 #e6e6e6,0 3px 13px rgba(0,0,0,.08);box-shadow:1px 0 #e6e6e6,-1px 0 #e6e6e6,0 1px #e6e6e6,0 -1px #e6e6e6,0 3px 13px #00000014}.flatpickr-calendar.open,.flatpickr-calendar.inline{opacity:1;max-height:640px;visibility:visible}.flatpickr-calendar.open{display:inline-block;z-index:99999}.flatpickr-calendar.animate.open{-webkit-animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1);animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1)}.flatpickr-calendar.inline{display:block;position:relative;top:2px}.flatpickr-calendar.static{position:absolute;top:calc(100% + 2px)}.flatpickr-calendar.static.open{z-index:999;display:block}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7){-webkit-box-shadow:none!important;box-shadow:none!important}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1){-webkit-box-shadow:-2px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-2px 0 #e6e6e6,5px 0 #e6e6e6}.flatpickr-calendar .hasWeeks .dayContainer,.flatpickr-calendar .hasTime .dayContainer{border-bottom:0;border-bottom-right-radius:0;border-bottom-left-radius:0}.flatpickr-calendar .hasWeeks .dayContainer{border-left:0}.flatpickr-calendar.hasTime .flatpickr-time{height:40px;border-top:1px solid #e6e6e6}.flatpickr-calendar.noCalendar.hasTime .flatpickr-time{height:auto}.flatpickr-calendar:before,.flatpickr-calendar:after{position:absolute;display:block;pointer-events:none;border:solid transparent;content:"";height:0;width:0;left:22px}.flatpickr-calendar.rightMost:before,.flatpickr-calendar.arrowRight:before,.flatpickr-calendar.rightMost:after,.flatpickr-calendar.arrowRight:after{left:auto;right:22px}.flatpickr-calendar.arrowCenter:before,.flatpickr-calendar.arrowCenter:after{left:50%;right:50%}.flatpickr-calendar:before{border-width:5px;margin:0 -5px}.flatpickr-calendar:after{border-width:4px;margin:0 -4px}.flatpickr-calendar.arrowTop:before,.flatpickr-calendar.arrowTop:after{bottom:100%}.flatpickr-calendar.arrowTop:before{border-bottom-color:#e6e6e6}.flatpickr-calendar.arrowTop:after{border-bottom-color:#fff}.flatpickr-calendar.arrowBottom:before,.flatpickr-calendar.arrowBottom:after{top:100%}.flatpickr-calendar.arrowBottom:before{border-top-color:#e6e6e6}.flatpickr-calendar.arrowBottom:after{border-top-color:#fff}.flatpickr-calendar:focus{outline:0}.flatpickr-wrapper{position:relative;display:inline-block}.flatpickr-months{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-months .flatpickr-month{background:transparent;color:#000000e6;fill:#000000e6;height:34px;line-height:1;text-align:center;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;overflow:hidden;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.flatpickr-months .flatpickr-prev-month,.flatpickr-months .flatpickr-next-month{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;text-decoration:none;cursor:pointer;position:absolute;top:0;height:34px;padding:10px;z-index:3;color:#000000e6;fill:#000000e6}.flatpickr-months .flatpickr-prev-month.flatpickr-disabled,.flatpickr-months .flatpickr-next-month.flatpickr-disabled{display:none}.flatpickr-months .flatpickr-prev-month i,.flatpickr-months .flatpickr-next-month i{position:relative}.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,.flatpickr-months .flatpickr-next-month.flatpickr-prev-month{left:0}.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,.flatpickr-months .flatpickr-next-month.flatpickr-next-month{right:0}.flatpickr-months .flatpickr-prev-month:hover,.flatpickr-months .flatpickr-next-month:hover{color:#959ea9}.flatpickr-months .flatpickr-prev-month:hover svg,.flatpickr-months .flatpickr-next-month:hover svg{fill:#f64747}.flatpickr-months .flatpickr-prev-month svg,.flatpickr-months .flatpickr-next-month svg{width:14px;height:14px}.flatpickr-months .flatpickr-prev-month svg path,.flatpickr-months .flatpickr-next-month svg path{-webkit-transition:fill .1s;transition:fill .1s;fill:inherit}.numInputWrapper{position:relative;height:auto}.numInputWrapper input,.numInputWrapper span{display:inline-block}.numInputWrapper input{width:100%}.numInputWrapper input::-ms-clear{display:none}.numInputWrapper input::-webkit-outer-spin-button,.numInputWrapper input::-webkit-inner-spin-button{margin:0;-webkit-appearance:none}.numInputWrapper span{position:absolute;right:0;width:14px;padding:0 4px 0 2px;height:50%;line-height:50%;opacity:0;cursor:pointer;border:1px solid rgba(57,57,57,.15);-webkit-box-sizing:border-box;box-sizing:border-box}.numInputWrapper span:hover{background:#0000001a}.numInputWrapper span:active{background:#0003}.numInputWrapper span:after{display:block;content:"";position:absolute}.numInputWrapper span.arrowUp{top:0;border-bottom:0}.numInputWrapper span.arrowUp:after{border-left:4px solid transparent;border-right:4px solid transparent;border-bottom:4px solid rgba(57,57,57,.6);top:26%}.numInputWrapper span.arrowDown{top:50%}.numInputWrapper span.arrowDown:after{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(57,57,57,.6);top:40%}.numInputWrapper span svg{width:inherit;height:auto}.numInputWrapper span svg path{fill:#00000080}.numInputWrapper:hover{background:#0000000d}.numInputWrapper:hover span{opacity:1}.flatpickr-current-month{font-size:135%;line-height:inherit;font-weight:300;color:inherit;position:absolute;width:75%;left:12.5%;padding:7.48px 0 0;line-height:1;height:34px;display:inline-block;text-align:center;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}.flatpickr-current-month span.cur-month{font-family:inherit;font-weight:700;color:inherit;display:inline-block;margin-left:.5ch;padding:0}.flatpickr-current-month span.cur-month:hover{background:#0000000d}.flatpickr-current-month .numInputWrapper{width:6ch;width:7ch\fffd;display:inline-block}.flatpickr-current-month .numInputWrapper span.arrowUp:after{border-bottom-color:#000000e6}.flatpickr-current-month .numInputWrapper span.arrowDown:after{border-top-color:#000000e6}.flatpickr-current-month input.cur-year{background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;color:inherit;cursor:text;padding:0 0 0 .5ch;margin:0;display:inline-block;font-size:inherit;font-family:inherit;font-weight:300;line-height:inherit;height:auto;border:0;border-radius:0;vertical-align:initial;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-current-month input.cur-year:focus{outline:0}.flatpickr-current-month input.cur-year[disabled],.flatpickr-current-month input.cur-year[disabled]:hover{font-size:100%;color:#00000080;background:transparent;pointer-events:none}.flatpickr-current-month .flatpickr-monthDropdown-months{appearance:menulist;background:transparent;border:none;border-radius:0;box-sizing:border-box;color:inherit;cursor:pointer;font-size:inherit;font-family:inherit;font-weight:300;height:auto;line-height:inherit;margin:-1px 0 0;outline:none;padding:0 0 0 .5ch;position:relative;vertical-align:initial;-webkit-box-sizing:border-box;-webkit-appearance:menulist;-moz-appearance:menulist;width:auto}.flatpickr-current-month .flatpickr-monthDropdown-months:focus,.flatpickr-current-month .flatpickr-monthDropdown-months:active{outline:none}.flatpickr-current-month .flatpickr-monthDropdown-months:hover{background:#0000000d}.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month{background-color:transparent;outline:none;padding:0}.flatpickr-weekdays{background:transparent;text-align:center;overflow:hidden;width:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:28px}.flatpickr-weekdays .flatpickr-weekdaycontainer{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}span.flatpickr-weekday{cursor:default;font-size:90%;background:transparent;color:#0000008a;line-height:1;margin:0;text-align:center;display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;font-weight:bolder}.dayContainer,.flatpickr-weeks{padding:1px 0 0}.flatpickr-days{position:relative;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start;width:307.875px}.flatpickr-days:focus{outline:0}.dayContainer{padding:0;outline:0;text-align:left;width:307.875px;min-width:307.875px;max-width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;display:inline-block;display:-ms-flexbox;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-wrap:wrap;-ms-flex-pack:justify;-webkit-justify-content:space-around;justify-content:space-around;-webkit-transform:translate3d(0,0,0);transform:translateZ(0);opacity:1}.dayContainer+.dayContainer{-webkit-box-shadow:-1px 0 0 #e6e6e6;box-shadow:-1px 0 #e6e6e6}.flatpickr-day{background:none;border:1px solid transparent;border-radius:150px;-webkit-box-sizing:border-box;box-sizing:border-box;color:#393939;cursor:pointer;font-weight:400;width:14.2857143%;-webkit-flex-basis:14.2857143%;-ms-flex-preferred-size:14.2857143%;flex-basis:14.2857143%;max-width:39px;height:39px;line-height:39px;margin:0;display:inline-block;position:relative;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;text-align:center}.flatpickr-day.inRange,.flatpickr-day.prevMonthDay.inRange,.flatpickr-day.nextMonthDay.inRange,.flatpickr-day.today.inRange,.flatpickr-day.prevMonthDay.today.inRange,.flatpickr-day.nextMonthDay.today.inRange,.flatpickr-day:hover,.flatpickr-day.prevMonthDay:hover,.flatpickr-day.nextMonthDay:hover,.flatpickr-day:focus,.flatpickr-day.prevMonthDay:focus,.flatpickr-day.nextMonthDay:focus{cursor:pointer;outline:0;background:#e6e6e6;border-color:#e6e6e6}.flatpickr-day.today{border-color:#959ea9}.flatpickr-day.today:hover,.flatpickr-day.today:focus{border-color:#959ea9;background:#959ea9;color:#fff}.flatpickr-day.selected,.flatpickr-day.startRange,.flatpickr-day.endRange,.flatpickr-day.selected.inRange,.flatpickr-day.startRange.inRange,.flatpickr-day.endRange.inRange,.flatpickr-day.selected:focus,.flatpickr-day.startRange:focus,.flatpickr-day.endRange:focus,.flatpickr-day.selected:hover,.flatpickr-day.startRange:hover,.flatpickr-day.endRange:hover,.flatpickr-day.selected.prevMonthDay,.flatpickr-day.startRange.prevMonthDay,.flatpickr-day.endRange.prevMonthDay,.flatpickr-day.selected.nextMonthDay,.flatpickr-day.startRange.nextMonthDay,.flatpickr-day.endRange.nextMonthDay{background:#569ff7;-webkit-box-shadow:none;box-shadow:none;color:#fff;border-color:#569ff7}.flatpickr-day.selected.startRange,.flatpickr-day.startRange.startRange,.flatpickr-day.endRange.startRange{border-radius:50px 0 0 50px}.flatpickr-day.selected.endRange,.flatpickr-day.startRange.endRange,.flatpickr-day.endRange.endRange{border-radius:0 50px 50px 0}.flatpickr-day.selected.startRange+.endRange:not(:nth-child(7n+1)),.flatpickr-day.startRange.startRange+.endRange:not(:nth-child(7n+1)),.flatpickr-day.endRange.startRange+.endRange:not(:nth-child(7n+1)){-webkit-box-shadow:-10px 0 0 #569ff7;box-shadow:-10px 0 #569ff7}.flatpickr-day.selected.startRange.endRange,.flatpickr-day.startRange.startRange.endRange,.flatpickr-day.endRange.startRange.endRange{border-radius:50px}.flatpickr-day.inRange{border-radius:0;-webkit-box-shadow:-5px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-5px 0 #e6e6e6,5px 0 #e6e6e6}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover,.flatpickr-day.prevMonthDay,.flatpickr-day.nextMonthDay,.flatpickr-day.notAllowed,.flatpickr-day.notAllowed.prevMonthDay,.flatpickr-day.notAllowed.nextMonthDay{color:#3939394d;background:transparent;border-color:transparent;cursor:default}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover{cursor:not-allowed;color:#3939391a}.flatpickr-day.week.selected{border-radius:0;-webkit-box-shadow:-5px 0 0 #569ff7,5px 0 0 #569ff7;box-shadow:-5px 0 #569ff7,5px 0 #569ff7}.flatpickr-day.hidden{visibility:hidden}.rangeMode .flatpickr-day{margin-top:1px}.flatpickr-weekwrapper{float:left}.flatpickr-weekwrapper .flatpickr-weeks{padding:0 12px;-webkit-box-shadow:1px 0 0 #e6e6e6;box-shadow:1px 0 #e6e6e6}.flatpickr-weekwrapper .flatpickr-weekday{float:none;width:100%;line-height:28px}.flatpickr-weekwrapper span.flatpickr-day,.flatpickr-weekwrapper span.flatpickr-day:hover{display:block;width:100%;max-width:none;color:#3939394d;background:transparent;cursor:default;border:none}.flatpickr-innerContainer{display:block;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden}.flatpickr-rContainer{display:inline-block;padding:0;-webkit-box-sizing:border-box;box-sizing:border-box}.flatpickr-time{text-align:center;outline:0;display:block;height:0;line-height:40px;max-height:40px;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-time:after{content:"";display:table;clear:both}.flatpickr-time .numInputWrapper{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;width:40%;height:40px;float:left}.flatpickr-time .numInputWrapper span.arrowUp:after{border-bottom-color:#393939}.flatpickr-time .numInputWrapper span.arrowDown:after{border-top-color:#393939}.flatpickr-time.hasSeconds .numInputWrapper{width:26%}.flatpickr-time.time24hr .numInputWrapper{width:49%}.flatpickr-time input{background:transparent;-webkit-box-shadow:none;box-shadow:none;border:0;border-radius:0;text-align:center;margin:0;padding:0;height:inherit;line-height:inherit;color:#393939;font-size:14px;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-time input.flatpickr-hour{font-weight:700}.flatpickr-time input.flatpickr-minute,.flatpickr-time input.flatpickr-second{font-weight:400}.flatpickr-time input:focus{outline:0;border:0}.flatpickr-time .flatpickr-time-separator,.flatpickr-time .flatpickr-am-pm{height:inherit;float:left;line-height:inherit;color:#393939;font-weight:700;width:2%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-align-self:center;-ms-flex-item-align:center;align-self:center}.flatpickr-time .flatpickr-am-pm{outline:0;width:18%;cursor:pointer;text-align:center;font-weight:400}.flatpickr-time input:hover,.flatpickr-time .flatpickr-am-pm:hover,.flatpickr-time input:focus,.flatpickr-time .flatpickr-am-pm:focus{background:#eee}.flatpickr-input[readonly]{cursor:pointer}@-webkit-keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}@keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}.workspace-leaf-content[data-type=kanban] .view-content{padding:0}.workspace-leaf-content[data-type=kanban]>.view-header{display:flex!important}.kanban-plugin{--lane-width: 272px}.kanban-plugin{contain:content;height:100%;width:100%;position:relative;display:flex;flex-direction:column}.kanban-plugin a.tag,.kanban-plugin__drag-container a.tag{padding-inline:var(--tag-padding-x);padding-block:var(--tag-padding-y)}.kanban-plugin__table-wrapper{height:100%;width:100%;overflow:auto;padding-block-end:40px;--table-column-first-border-width: 0;--table-column-last-border-width: 0;--table-row-last-border-width: 0}.kanban-plugin__table-wrapper table{width:fit-content;margin-block:0;margin-inline:auto;box-shadow:0 0 0 var(--table-border-width) var(--table-border-color)}.kanban-plugin__table-wrapper tr{width:fit-content}.kanban-plugin__table-wrapper th,.kanban-plugin__table-wrapper td{text-align:start;vertical-align:top;font-size:.875rem;padding:0!important;height:1px}.kanban-plugin__table-wrapper th.mod-has-icon .kanban-plugin__table-cell-wrapper,.kanban-plugin__table-wrapper td.mod-has-icon .kanban-plugin__table-cell-wrapper{padding-inline-end:var(--size-2-2)}.kanban-plugin__table-wrapper th .kanban-plugin__table-cell-wrapper,.kanban-plugin__table-wrapper td .kanban-plugin__table-cell-wrapper{height:100%;padding-inline:var(--size-4-2);padding-block:var(--size-2-2)}.kanban-plugin__table-wrapper th .kanban-plugin__item-prefix-button-wrapper input[type=checkbox],.kanban-plugin__table-wrapper td .kanban-plugin__item-prefix-button-wrapper input[type=checkbox]{margin-block:2px}.kanban-plugin__table-wrapper th:has(.markdown-source-view),.kanban-plugin__table-wrapper td:has(.markdown-source-view){--background-primary: var(--background-primary-alt);background:var(--background-primary);outline:2px solid var(--background-modifier-border-focus)}.kanban-plugin__table-wrapper thead tr>th{height:1px;background-color:var(--background-primary);position:sticky;top:0;z-index:1;overflow:visible}.kanban-plugin__table-wrapper thead tr>th:nth-child(2n+2){background-color:var(--background-primary)}.kanban-plugin__table-wrapper thead tr>th .kanban-plugin__table-cell-wrapper{height:100%;padding-block:var(--size-2-2);padding-inline:var(--size-4-2) var(--size-2-2);box-shadow:0 0 0 var(--table-border-width) var(--table-border-color)}.kanban-plugin__table-wrapper .resizer{position:absolute;top:0;height:100%;width:5px;background:var(--table-selection-border-color);cursor:col-resize;user-select:none;touch-action:none}.kanban-plugin__table-wrapper .resizer.ltr{right:0}.kanban-plugin__table-wrapper .resizer.rtl{left:0}.kanban-plugin__table-wrapper .resizer.isResizing{opacity:1}@media (hover: hover){.kanban-plugin__table-wrapper .resizer{opacity:0}.kanban-plugin__table-wrapper .resizer:hover{opacity:1}}.kanban-plugin__table-wrapper .kanban-plugin__item-tags:not(:empty){margin-block-start:-5px}.kanban-plugin__table-wrapper .kanban-plugin__item-metadata-date-relative{display:block}.kanban-plugin__table-wrapper .kanban-plugin__item-input-wrapper,.kanban-plugin__table-wrapper .cm-table-widget,.kanban-plugin__table-wrapper .kanban-plugin__item-title,.kanban-plugin__table-wrapper .kanban-plugin__item-title-wrapper,.kanban-plugin__table-wrapper .kanban-plugin__item-content-wrapper{height:100%}.kanban-plugin__table-wrapper .kanban-plugin__item-title-wrapper{padding:0}.kanban-plugin .markdown-source-view.mod-cm6{display:block;font-size:.875rem}.kanban-plugin .markdown-source-view.mod-cm6 .cm-scroller{overflow:visible}.kanban-plugin__table-header{display:flex;gap:var(--size-4-2);align-items:center;justify-content:space-between}.kanban-plugin__table-header-sort{line-height:1;color:var(--text-faint);padding:2px;border-radius:4px}.kanban-plugin__table-header-sort>span{display:flex}div:hover>.kanban-plugin__table-header-sort{background-color:var(--background-modifier-hover)}.kanban-plugin__cell-flex-wrapper{display:flex;gap:8px;align-items:flex-start;justify-content:space-between}.kanban-plugin__cell-flex-wrapper .lucide-more-vertical{transform:none}.kanban-plugin__icon-wrapper{display:flex;line-height:1}.kanban-plugin__icon-wrapper>.kanban-plugin__icon{display:flex}.kanban-plugin.something-is-dragging{cursor:grabbing;cursor:-webkit-grabbing}.kanban-plugin.something-is-dragging *{pointer-events:none}.kanban-plugin__item button,.kanban-plugin__lane button,.kanban-plugin button{line-height:1;margin:0;transition:.1s color,.1s background-color}.kanban-plugin__search-wrapper{width:100%;position:sticky;top:0;left:0;padding-block:10px;padding-inline:13px;display:flex;justify-content:flex-end;align-items:center;z-index:2;background-color:var(--background-primary)}button.kanban-plugin__search-cancel-button{display:flex;line-height:1;padding:6px;border:1px solid var(--background-modifier-border);background:var(--background-secondary-alt);color:var(--text-muted);margin-block:0;margin-inline:5px 0;font-size:16px}button.kanban-plugin__search-cancel-button .kanban-plugin__icon{display:flex}.kanban-plugin__icon{display:inline-block;line-height:1;--icon-size: 1em}.kanban-plugin__board{display:flex;width:100%;height:100%}.kanban-plugin__board>div{display:flex;align-items:flex-start;justify-content:flex-start;padding:1rem;width:fit-content;height:100%}.kanban-plugin__board.kanban-plugin__vertical>div{height:fit-content;width:100%;flex-direction:column}.is-mobile .view-content:not(.is-mobile-editing) .kanban-plugin__board>div{padding-bottom:calc(1rem + var(--mobile-navbar-height))}.kanban-plugin__board.is-adding-lane>div{padding-inline-end:calc(250px + 1rem)}.kanban-plugin__lane-wrapper{display:flex;flex-shrink:0;margin-inline-end:10px;max-height:100%;width:var(--lane-width)}.kanban-plugin__vertical .kanban-plugin__lane-wrapper{margin-block-end:10px;margin-inline-end:0}.kanban-plugin__lane{width:100%;display:flex;flex-direction:column;background-color:var(--background-secondary);border-radius:6px;border:1px solid var(--background-modifier-border)}.is-dropping>.kanban-plugin__lane{background-color:hsla(var(--interactive-accent-hsl),.15);border-color:hsla(var(--interactive-accent-hsl),1);outline:1px solid hsla(var(--interactive-accent-hsl),1)}.kanban-plugin__placeholder.kanban-plugin__lane-placeholder{height:100%;flex-grow:1;margin-inline-end:5px}.kanban-plugin__lane.is-hidden{display:none}.kanban-plugin__lane button{padding-block:8px;padding-inline:10px}.kanban-plugin__lane-form-wrapper{position:absolute;top:1rem;right:1rem;width:250px;background-color:var(--background-secondary);border-radius:6px;border:2px solid hsla(var(--interactive-accent-hsl),.7);z-index:var(--layer-popover);box-shadow:0 .5px 1px .5px #0000001a,0 2px 10px #0000001a,0 10px 20px #0000001a}.kanban-plugin__lane-input{--font-text-size: var(--font-ui-small);padding-block:var(--size-4-1);padding-inline:var(--size-4-2);background-color:var(--background-primary);border-radius:var(--radius-s)}.kanban-plugin__lane-input-wrapper{padding:10px}.kanban-plugin__item-input-actions,.kanban-plugin__lane-input-actions{display:flex;align-items:flex-start;justify-content:flex-start;padding-block:0 10px;padding-inline:10px}.kanban-plugin__item-input-actions button,.kanban-plugin__lane-input-actions button{display:block;margin-inline-end:5px}button.kanban-plugin__item-action-add,button.kanban-plugin__lane-action-add{background-color:var(--interactive-accent);color:var(--text-on-accent)}button.kanban-plugin__item-action-add:hover,button.kanban-plugin__lane-action-add:hover{background-color:var(--interactive-accent-hover)}.kanban-plugin__lane-header-wrapper{padding-block:8px;padding-inline:8px 12px;display:flex;align-items:center;gap:var(--size-4-1);flex-shrink:0;flex-grow:0;border-bottom:1px solid var(--background-modifier-border)}.collapse-horizontal .kanban-plugin__lane-header-wrapper,.collapse-vertical .kanban-plugin__lane-header-wrapper,.will-prepend .kanban-plugin__lane-header-wrapper{border-bottom:none}.kanban-plugin__lane-wrapper.collapse-horizontal{width:auto}.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-header-wrapper{writing-mode:vertical-lr}.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-header-wrapper{gap:var(--size-4-2)}.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-title-count,.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-title-text{transform:rotate(180deg)}.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-settings-button-wrapper{display:none}.kanban-plugin__lane-wrapper.collapse-vertical .kanban-plugin__lane-settings-button-wrapper{visibility:hidden}.kanban-plugin__lane-collapse{flex-grow:0;color:var(--text-faint)}.kanban-plugin__lane-collapse>span{display:flex}.collapse-vertical .kanban-plugin__lane-collapse>span{transform:rotate(-90deg)}.kanban-plugin__lane-grip{cursor:grab;flex-grow:0;color:var(--text-faint)}.kanban-plugin__lane-grip:active{cursor:grabbing}.kanban-plugin__lane-collapse svg{--icon-size: 1rem}.kanban-plugin__lane-grip>svg{height:1rem;display:block}.kanban-plugin__lane-title{font-weight:600;font-size:.875rem;flex-grow:1;width:100%;display:flex;flex-direction:column}.kanban-plugin__lane-title-text{flex-grow:1}div.kanban-plugin__lane-title-count{border-radius:3px;color:var(--text-muted);display:block;font-size:13px;line-height:1;padding:4px}div.kanban-plugin__lane-title-count.wip-exceeded{font-weight:700;color:var(--text-normal);background-color:rgba(var(--background-modifier-error-rgb),.25)}.kanban-plugin__table-cell-wrapper .kanban-plugin__lane-menu,.kanban-plugin__table-cell-wrapper .kanban-plugin__item-prefix-button,.kanban-plugin__item .kanban-plugin__item-prefix-button,.kanban-plugin__item .kanban-plugin__item-postfix-button,.kanban-plugin__lane .kanban-plugin__lane-settings-button{--icon-stroke: 2.5px;font-size:13px;line-height:1;color:var(--text-muted);padding:4px;display:flex;margin-inline-end:-4px}.kanban-plugin__table-cell-wrapper .kanban-plugin__lane-menu.is-enabled,.kanban-plugin__table-cell-wrapper .kanban-plugin__item-prefix-button.is-enabled,.kanban-plugin__item .kanban-plugin__item-prefix-button.is-enabled,.kanban-plugin__item .kanban-plugin__item-postfix-button.is-enabled,.kanban-plugin__lane .kanban-plugin__lane-settings-button.is-enabled{color:var(--text-accent)}.kanban-plugin__table-cell-wrapper .kanban-plugin__lane-menu{color:var(--text-faint);margin-inline-start:2px;margin-inline-end:0px}.kanban-plugin__table-cell-wrapper .kanban-plugin__item-prefix-button,.kanban-plugin__item .kanban-plugin__item-prefix-button{margin-inline-end:4px;margin-inline-start:-4px}.kanban-plugin__table-cell-wrapper button.kanban-plugin__item-prefix-button,.kanban-plugin__item button.kanban-plugin__item-prefix-button{margin-block:4px;margin-inline:0 7px;padding:0}.kanban-plugin__lane-action-wrapper,.kanban-plugin__item-edit-archive-button,.kanban-plugin__item-settings-actions .kanban-plugin__icon,.kanban-plugin__item-edit-archive-button>.kanban-plugin__icon,.kanban-plugin__item-prefix-button>.kanban-plugin__icon,.kanban-plugin__item-postfix-button>.kanban-plugin__icon,.kanban-plugin__lane-settings-button>.kanban-plugin__icon{display:flex}.kanban-plugin__lane-settings-button-wrapper{display:flex;gap:4px}button.kanban-plugin__lane-settings-button+button.kanban-plugin__lane-settings-button{margin-inline-start:2px}.kanban-plugin__lane-settings-button svg{width:1em;height:1em}.kanban-plugin__lane-items-wrapper{margin:4px;height:100%}.kanban-plugin__lane-items{padding:4px;margin-block:0;margin-inline:4px;display:flex;flex-direction:column}.kanban-plugin__lane-items>div{margin-block-start:4px}.kanban-plugin__lane-items>.kanban-plugin__placeholder{flex-grow:1}.kanban-plugin__lane-items>.kanban-plugin__placeholder:only-child{height:2.55em;border:3px dashed rgba(var(--text-muted-rgb),.1);margin-block-end:4px;border-radius:6px;transition:border .2s ease}.is-sorting .kanban-plugin__lane-items>.kanban-plugin__placeholder:only-child{border-color:hsla(var(--interactive-accent-hsl),.6)}.kanban-plugin__item-button-wrapper{border-top:1px solid var(--background-modifier-border);padding:8px;flex-shrink:0;flex-grow:0}.kanban-plugin__item-button-wrapper>button{text-align:left;width:100%}.kanban-plugin__lane-header-wrapper+.kanban-plugin__item-button-wrapper{border-top:none;border-bottom:1px solid var(--background-modifier-border);padding-block:4px 8px;padding-inline:8px}.kanban-plugin__item-form{border-top:1px solid var(--background-modifier-border);padding:8px}.kanban-plugin__item-form .kanban-plugin__item-input-wrapper{padding-block:6px;padding-inline:8px;border:1px solid var(--background-modifier-border);background-color:var(--background-primary);border-radius:var(--input-radius);min-height:var(--input-height)}.kanban-plugin__lane-header-wrapper+.kanban-plugin__item-form{border-top:none;border-bottom:1px solid var(--background-modifier-border);padding-block:4px 8px;padding-inline:8px}.kanban-plugin__item-input-wrapper{--line-height-normal: var(--line-height-tight);display:flex;flex-direction:column;flex-grow:1}.kanban-plugin button.kanban-plugin__item-submit-button{flex-grow:0;flex-shrink:1;font-size:14px;height:auto;line-height:1;margin-block-start:5px;width:auto}.is-mobile .kanban-plugin button.kanban-plugin__item-submit-button{font-size:12px}.is-mobile .kanban-plugin__lane-form-wrapper{--input-height: auto}.is-mobile .kanban-plugin__lane-form-wrapper button{padding-block:var(--size-4-2)}.is-mobile .kanban-plugin__lane-form-wrapper .markdown-source-view.mod-cm6{font-size:var(--font-ui-medium)}.is-mobile .kanban-plugin .kanban-plugin__lane-input-wrapper button.kanban-plugin__item-submit-button{display:none}button.kanban-plugin__new-item-button{background-color:transparent;color:var(--text-muted)}.kanban-plugin__new-item-button:hover{color:var(--text-on-accent);background-color:var(--interactive-accent-hover)}.kanban-plugin__drag-container>.kanban-plugin__item-wrapper .kanban-plugin__item{border-color:var(--interactive-accent);box-shadow:var(--shadow-s),0 0 0 2px hsla(var(--interactive-accent-hsl),.7)}.kanban-plugin__item{font-size:.875rem;border:1px solid var(--background-modifier-border);border-radius:var(--input-radius);overflow:hidden;transition:.3s opacity cubic-bezier(.25,1,.5,1)}.kanban-plugin__item:has(.markdown-source-view){outline:1px solid var(--background-modifier-border-focus);border-color:var(--background-modifier-border-focus)}.kanban-plugin__item-content-wrapper{background:var(--background-primary)}.kanban-plugin__item-title-wrapper{background:var(--background-primary);display:flex;padding-block:6px;padding-inline:8px}.kanban-plugin__item-title-wrapper:not(:only-child){border-bottom:1px solid var(--background-modifier-border)}.kanban-plugin__item-title{width:100%;line-height:var(--line-height-tight);margin-block-start:1px}.kanban-plugin__meta-value,.kanban-plugin__markdown-preview-wrapper{white-space:pre-wrap;white-space:break-spaces;word-break:break-word;overflow-wrap:anywhere;--font-text-size: .875rem;--line-height-normal: var(--line-height-tight);--p-spacing: var(--size-4-2);--list-indent: 1.75em}.kanban-plugin__meta-value .markdown-preview-view,.kanban-plugin__markdown-preview-wrapper .markdown-preview-view{--file-margins: 0}.kanban-plugin__meta-value.inline,.kanban-plugin__markdown-preview-wrapper.inline{display:inline-block}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view>*:first-child,.kanban-plugin__markdown-preview-wrapper .kanban-plugin__markdown-preview-view>*:first-child{margin-block-start:0}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view>*:last-child,.kanban-plugin__markdown-preview-wrapper .kanban-plugin__markdown-preview-view>*:last-child{margin-block-end:0}.kanban-plugin__meta-value .markdown-preview-view,.kanban-plugin__markdown-preview-wrapper .markdown-preview-view{width:unset;height:unset;position:unset;overflow-y:unset;overflow-wrap:unset;color:unset;user-select:unset;-webkit-user-select:unset;white-space:normal}.kanban-plugin__meta-value .markdown-preview-view .markdown-embed,.kanban-plugin__markdown-preview-wrapper .markdown-preview-view .markdown-embed,.kanban-plugin__meta-value .markdown-preview-view blockquote,.kanban-plugin__markdown-preview-wrapper .markdown-preview-view blockquote{padding-inline:var(--size-4-2) 0;padding-block:var(--size-4-1);margin-block-start:var(--p-spacing);margin-block-end:var(--p-spacing)}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view{display:inline-flex}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view>div:first-child>*:first-child{margin-block-start:0}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view>div:last-child>*:last-child{margin-block-end:0}.kanban-plugin__embed-link-wrapper{padding:2px;float:right}.kanban-plugin__item-metadata-wrapper:not(:empty){background-color:var(--background-primary-alt);padding-inline:8px;padding-block:6px}.kanban-plugin__item-metadata:not(:empty){padding-block-start:5px;font-size:12px}.kanban-plugin__item-metadata:not(:empty) .markdown-preview-view{line-height:var(--line-height-tight);font-size:inherit}.kanban-plugin__item-metadata>span{display:block}.kanban-plugin__item-metadata>span.kanban-plugin__item-metadata-date-wrapper{display:inline-block}.kanban-plugin__item-metadata .is-button{cursor:var(--cursor)}.kanban-plugin__item-metadata .is-button:hover{color:var(--text-normal)}.kanban-plugin__item-metadata-date-relative:first-letter{text-transform:uppercase}.kanban-plugin__item-metadata a{text-decoration:none}.kanban-plugin__item-task-inline-metadata-item,.kanban-plugin__item-task-metadata-item{display:inline-flex;margin-block:3px 0;margin-inline:0 6px;gap:4px}.kanban-plugin__item-task-inline-metadata-item{padding-inline:2px;background-color:var(--background-secondary);border-radius:var(--radius-s)}.kanban-plugin__table-cell-wrapper .kanban-plugin__item-task-inline-metadata-item{background-color:unset;padding-inline:unset;border-radius:unset}.kanban-plugin__item-tags:not(:empty){padding-block-start:2px}.kanban-plugin__item-tag{display:inline-block;margin-inline-end:4px}.kanban-plugin__item-tags .kanban-plugin__item-tag{font-size:12px;background-color:var(--tag-background, hsla(var(--interactive-accent-hsl), .1));color:var(--tag-color, var(--text-accent));margin-block:3px 0;margin-inline:0 3px}.kanban-plugin__item-tag.is-search-match,.kanban-plugin__item-tags .kanban-plugin__item-tag.is-search-match{background-color:var(--text-highlight-bg);color:var(--text-normal)}.kanban-plugin__meta-table{width:100%;margin:0;line-height:var(--line-height-tight);font-size:.75rem}.kanban-plugin__meta-table .markdown-preview-view{font-size:.75rem}.kanban-plugin__meta-table .kanban-plugin__item-tags .kanban-plugin__item-tag{position:relative;inset-block-start:-2px;margin-block:0 3px}.kanban-plugin__meta-table td{vertical-align:top;padding-block:3px 0;padding-inline:0;width:10%}.kanban-plugin__meta-table td+td{width:90%}.kanban-plugin__meta-table td:only-child{width:100%}.kanban-plugin__meta-table td.kanban-plugin__meta-key{white-space:nowrap;padding-inline-end:5px;color:var(--text-muted)}.kanban-plugin__meta-table td.kanban-plugin__meta-key.is-search-match>span{background-color:var(--text-highlight-bg);color:var(--text-normal)}.kanban-plugin__meta-value:not(.mod-array){white-space:pre-wrap;display:flex}.kanban-plugin__meta-value>.is-search-match,.kanban-plugin__meta-value.is-search-match{background-color:var(--text-highlight-bg);color:var(--text-normal)}.kanban-plugin__item-prefix-button-wrapper,.kanban-plugin__item-postfix-button-wrapper{display:flex;flex-grow:0;flex-shrink:0;align-self:start}.kanban-plugin__item-prefix-button-wrapper>div,.kanban-plugin__item-postfix-button-wrapper>div{display:flex;flex-direction:column;gap:var(--size-4-1)}.kanban-plugin__item-prefix-button-wrapper{flex-direction:column}.kanban-plugin__item-prefix-button-wrapper .kanban-plugin__item-prefix-button{width:var(--checkbox-size);height:var(--checkbox-size)}.kanban-plugin__item-prefix-button-wrapper .kanban-plugin__item-prefix-button,.kanban-plugin__item-prefix-button-wrapper input[type=checkbox]{margin-block:2px;margin-inline:0px 7px}.kanban-plugin__item-prefix-button-wrapper .kanban-plugin__item-prefix-button+button,.kanban-plugin__item-prefix-button-wrapper input[type=checkbox]+button{margin-block-start:10px}button.kanban-plugin__item-postfix-button{visibility:hidden;opacity:0;transition:.1s opacity;display:flex;align-self:flex-start}button.kanban-plugin__item-postfix-button.is-enabled,.kanban-plugin__item:hover button.kanban-plugin__item-postfix-button{visibility:visible;opacity:1}.kanban-plugin__item-settings-actions{padding:5px;display:flex}.kanban-plugin__item-settings-actions>button{line-height:1;display:flex;align-items:center;justify-content:center;font-size:.75rem;width:100%}.kanban-plugin__lane-action-wrapper button>.kanban-plugin__icon,.kanban-plugin__item-settings-actions button>.kanban-plugin__icon{margin-inline-end:5px}.kanban-plugin__item-settings-actions>button:first-child,.kanban-plugin__lane-action-wrapper>button:first-child{margin-inline-end:2.5px}.kanban-plugin__item-settings-actions>button:last-child,.kanban-plugin__lane-action-wrapper>button:last-child{margin-inline-start:2.5px}.kanban-plugin__archive-lane-button,.kanban-plugin__item-button-archive{color:var(--text-muted);border:1px solid var(--background-modifier-border)}.kanban-plugin__archive-lane-button:hover,.kanban-plugin__item-button-archive:hover{color:var(--text-normal)}.kanban-plugin__item-button-delete{border:1px solid rgba(var(--background-modifier-error-rgb),.15);color:rgba(var(--background-modifier-error-rgb),1)}.kanban-plugin__item-button-delete:hover{background-color:rgba(var(--background-modifier-error-rgb),.2);color:var(--text-error)}.theme-dark .kanban-plugin__item-button-delete{background-color:transparent;border:1px solid rgba(var(--background-modifier-error-rgb),1);color:var(--text-error)}.theme-dark .kanban-plugin__item-button-delete:hover{background-color:rgba(var(--background-modifier-error-rgb),1);color:var(--text-error)}.kanban-plugin__checkbox-wrapper{border-top:1px solid var(--background-modifier-border);border-bottom:1px solid var(--background-modifier-border);padding:10px;margin-block-end:10px;display:flex;align-items:center}.kanban-plugin__checkbox-wrapper .checkbox-container{flex-shrink:0;flex-grow:0;margin-inline-start:15px}.kanban-plugin__checkbox-label{font-size:.8125rem;line-height:var(--line-height-tight)}.kanban-plugin__lane-setting-wrapper>div{border-top:none;border-bottom:none;padding-block:10px;padding-inline:15px;margin-block-end:0}.kanban-plugin__lane-setting-wrapper>div:last-child{border-bottom:1px solid var(--background-modifier-border);margin-block-end:10px}.kanban-plugin__action-confirm-wrapper{border:1px solid rgba(var(--background-modifier-error-rgb),.2);background-color:rgba(var(--background-modifier-error-rgb),.1);border-radius:4px;padding:10px;margin-block:5px;margin-inline:10px}.theme-dark .kanban-plugin__action-confirm-wrapper{border:1px solid rgba(var(--background-modifier-error-rgb),1)}.kanban-plugin__delete-lane-button,.kanban-plugin__archive-lane-button{display:flex;align-items:center;justify-content:center;font-size:.75rem;width:50%}.kanban-plugin__delete-lane-button{border:1px solid rgba(var(--background-modifier-error-rgb),.15);color:rgba(var(--background-modifier-error-rgb),1)}.kanban-plugin__delete-lane-button:hover{background-color:rgba(var(--background-modifier-error-rgb),.2);color:var(--text-error)}.theme-dark .kanban-plugin__delete-lane-button{background-color:transparent;border:1px solid rgba(var(--background-modifier-error-rgb),1);color:var(--text-error)}.theme-dark .kanban-plugin__delete-lane-button:hover{background-color:rgba(var(--background-modifier-error-rgb),1);color:var(--text-error)}.kanban-plugin__action-confirm-text{font-size:.875rem;color:var(--text-error);margin-block-end:10px;line-height:var(--line-height-tight)}button.kanban-plugin__confirm-action-button{border:1px solid rgba(var(--background-modifier-error-rgb),.2);margin-inline-end:5px;color:var(--text-error)}button.kanban-plugin__confirm-action-button:hover{background-color:rgba(var(--background-modifier-error-rgb),.5)}button.kanban-plugin__cancel-action-button{border:1px solid var(--background-modifier-border)}.modal.kanban-plugin__board-settings-modal{width:var(--modal-width);height:var(--modal-height);max-height:var(--modal-max-height);max-width:var(--modal-max-width);padding:0;display:flex;flex-direction:column}.modal.kanban-plugin__board-settings-modal .modal-content{padding-block:30px;padding-inline:50px;height:100%;overflow-y:auto;overflow-x:hidden;margin:0}.kanban-plugin__board-settings-modal .setting-item{flex-wrap:wrap;justify-content:space-between}.kanban-plugin__board-settings-modal .setting-item-info{max-width:400px;min-width:300px;width:50%}.kanban-plugin__board-settings-modal .setting-item-control{min-width:300px;flex-shrink:0}.kanban-plugin__board-settings-modal .choices{width:100%;text-align:left}.kanban-plugin__board-settings-modal .choices[data-type*=select-one] .choices__inner{background-color:var(--background-primary);border-color:var(--background-modifier-border);padding:0;min-height:0}.kanban-plugin__board-settings-modal .choices[data-type*=select-one] .choices__input{background-color:var(--background-primary);border-bottom-color:var(--background-modifier-border);font-size:14px}.kanban-plugin__board-settings-modal .choices__input{border-radius:0;border-top:none;border-left:none;border-right:none}.kanban-plugin__board-settings-modal .choices__list[role=listbox]{overflow-x:hidden}.kanban-plugin__board-settings-modal .choices__list--single{padding-block:4px;padding-inline:6px 20px}.kanban-plugin__board-settings-modal .is-open .choices__list--dropdown,.kanban-plugin__board-settings-modal .choices__list--dropdown{background-color:var(--background-primary);border-color:var(--background-modifier-border);word-break:normal;max-height:200px;display:flex;flex-direction:column}.kanban-plugin__board-settings-modal .choices__list--dropdown .choices__item--selectable:after{display:none}.kanban-plugin__board-settings-modal .choices__list--dropdown .choices__item--selectable{padding-block:4px;padding-inline:6px}.kanban-plugin__board-settings-modal .choices__list--dropdown .choices__item.is-highlighted{background-color:var(--background-primary-alt)}.kanban-plugin__board-settings-modal .choices__placeholder{opacity:1;color:var(--text-muted)}.kanban-plugin__board-settings-modal .error{border-color:var(--background-modifier-error-hover)!important}.kanban-plugin__date-picker{position:absolute;z-index:var(--layer-popover);--cell-size: 2.4em}.kanban-plugin__date-picker .flatpickr-input{width:0;height:0;opacity:0;border:none;padding:0;display:block;margin-block-end:-1px}.kanban-plugin__date-picker .flatpickr-current-month{color:var(--text-normal);font-weight:600;font-size:inherit;width:100%;position:static;height:auto;display:flex;align-items:center;justify-content:center;padding:0}.kanban-plugin__date-picker .flatpickr-current-month .numInputWrapper span.arrowUp:after{border-bottom-color:var(--text-normal)}.kanban-plugin__date-picker .flatpickr-current-month .numInputWrapper span.arrowDown:after{border-top-color:var(--text-normal)}.flatpickr-months .flatpickr-prev-month svg path,.flatpickr-months .flatpickr-next-month svg path{fill:currentColor}.kanban-plugin__date-picker .flatpickr-calendar{border-radius:var(--radius-m);font-size:13px;overflow:hidden;background-color:var(--background-primary);width:calc(var(--cell-size) * 7 + 8px);box-shadow:0 0 0 1px var(--background-modifier-border),0 15px 25px #0003}.kanban-plugin__date-picker .flatpickr-calendar.inline{top:0}.kanban-plugin__date-picker .flatpickr-months{font-size:13px;padding-block:2px 4px;padding-inline:2px;align-items:center}.kanban-plugin__date-picker .flatpickr-months .flatpickr-current-month input.cur-year,.kanban-plugin__date-picker .flatpickr-months select{border-radius:4px;padding:4px}.kanban-plugin__date-picker .flatpickr-months .numInputWrapper{border-radius:4px}.kanban-plugin__date-picker .flatpickr-months .flatpickr-month{width:100%;height:auto}.kanban-plugin__date-picker .flatpickr-months .flatpickr-prev-month,.kanban-plugin__date-picker .flatpickr-months .flatpickr-next-month{color:var(--text-normal);fill:currentColor;border-radius:4px;display:flex;align-items:center;justify-content:center;line-height:1;height:auto;padding:5px;position:static;flex-shrink:0}.kanban-plugin__date-picker .flatpickr-months .flatpickr-prev-month:hover,.kanban-plugin__date-picker .flatpickr-months .flatpickr-next-month:hover{background-color:var(--background-primary-alt);color:var(--text-normal)}.kanban-plugin__date-picker .flatpickr-months .flatpickr-prev-month:hover svg,.kanban-plugin__date-picker .flatpickr-months .flatpickr-next-month:hover svg{fill:currentColor}.kanban-plugin__date-picker .flatpickr-current-month .flatpickr-monthDropdown-months{box-shadow:none;color:var(--text-normal);font-weight:inherit;margin-inline-end:5px}.kanban-plugin__date-picker .flatpickr-current-month input.cur-year{color:var(--text-normal);font-weight:inherit}.kanban-plugin__date-picker .flatpickr-weekdays{height:auto;padding-block:8px 12px;padding-inline:0}.kanban-plugin__date-picker span.flatpickr-weekday{font-weight:400;color:var(--text-muted)}.kanban-plugin__date-picker .flatpickr-innerContainer{padding:4px}.kanban-plugin__date-picker .flatpickr-day{color:var(--text-normal);display:inline-flex;align-items:center;justify-content:center;width:var(--cell-size);height:var(--cell-size);line-height:1;border-radius:6px}.kanban-plugin__date-picker .flatpickr-day.today{border-color:var(--interactive-accent)}.kanban-plugin__date-picker .flatpickr-day.today:hover{color:var(--text-normal);border-color:var(--interactive-accent);background-color:var(--background-primary-alt)}.kanban-plugin__date-picker .flatpickr-day.selected{border-color:var(--interactive-accent);background-color:var(--interactive-accent);color:var(--text-on-accent)}.kanban-plugin__date-picker .flatpickr-day.selected:hover{border-color:var(--interactive-accent);background-color:var(--interactive-accent)}.kanban-plugin__date-picker .flatpickr-days{width:calc(var(--cell-size) * 7)}.kanban-plugin__date-picker .dayContainer{width:calc(var(--cell-size) * 7);min-width:calc(var(--cell-size) * 7);max-width:calc(var(--cell-size) * 7)}.kanban-plugin__date-picker .flatpickr-day.inRange,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay.inRange,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay.inRange,.kanban-plugin__date-picker .flatpickr-day.today.inRange,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay.today.inRange,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay.today.inRange,.kanban-plugin__date-picker .flatpickr-day:hover,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay:hover,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay:hover,.kanban-plugin__date-picker .flatpickr-day:focus,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay:focus,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay:focus{background-color:var(--background-primary-alt);border-color:var(--background-primary-alt)}.kanban-plugin__date-picker .flatpickr-day.flatpickr-disabled,.kanban-plugin__date-picker .flatpickr-day.flatpickr-disabled:hover,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay,.kanban-plugin__date-picker .flatpickr-day.notAllowed,.kanban-plugin__date-picker .flatpickr-day.notAllowed.prevMonthDay,.kanban-plugin__date-picker .flatpickr-day.notAllowed.nextMonthDay{color:var(--text-faint)}.kanban-plugin__time-picker{position:absolute;max-height:250px;overflow:auto;border-radius:4px;border:1px solid var(--background-modifier-border);box-shadow:0 2px 8px var(--background-modifier-box-shadow);background:var(--background-primary);color:var(--text-normal);font-size:14px;z-index:var(--layer-menu)}.kanban-plugin__time-picker-item{display:flex;align-items:center;color:var(--text-muted);cursor:var(--cursor);line-height:1;padding-block:6px;padding-inline:8px}.kanban-plugin__time-picker-check{visibility:hidden;display:inline-flex;margin-inline-end:5px}.kanban-plugin__time-picker-item.is-hour{color:var(--text-normal);font-weight:600}.kanban-plugin__time-picker-item.is-selected .kanban-plugin__time-picker-check{visibility:visible}.kanban-plugin__time-picker-item:hover,.kanban-plugin__time-picker-item.is-selected{background:var(--background-secondary)}.kanban-plugin mark{background-color:var(--text-highlight-bg)}.kanban-plugin__draggable-setting-container{border-top:0;padding:0;flex-direction:column}.kanban-plugin__draggable-setting-container>div{width:100%;margin-inline-end:0!important}.kanban-plugin__setting-item-wrapper{border-top:1px solid var(--background-modifier-border)}.kanban-plugin__draggable-setting-container>.kanban-plugin__placeholder{border-top:1px solid var(--background-modifier-border)}.kanban-plugin__setting-item{background-color:var(--background-secondary);width:100%;font-size:16px;display:flex;align-items:flex-start;padding:12px;color:var(--text-muted)}.kanban-plugin__drag-container .kanban-plugin__setting-item{border:1px solid hsla(var(--interactive-accent-hsl),.8);box-shadow:0 15px 25px #0003,0 0 0 2px hsla(var(--interactive-accent-hsl),.8)}.kanban-plugin__setting-controls-wrapper{flex-grow:1;flex-shrink:1}.kanban-plugin__setting-input-wrapper{display:flex;flex-wrap:wrap;margin-block-end:1rem}.kanban-plugin__setting-input-wrapper>div{margin-inline-end:10px}.kanban-plugin__setting-toggle-wrapper>div{display:flex;align-items:center;line-height:1;margin-block-end:10px}.kanban-plugin__setting-toggle-wrapper .checkbox-container{margin-inline-end:10px}.kanban-plugin__setting-button-wrapper{display:flex;justify-content:flex-end;flex-grow:1;flex-shrink:0;max-width:25px}.kanban-plugin__setting-button-wrapper>div{margin-inline-start:12px}.kanban-plugin__setting-key-input-wrapper{margin-block:1rem;margin-inline:0}.kanban-plugin__setting-key-input-wrapper>input{margin-inline-end:10px}.kanban-plugin__date-color-input-wrapper,.kanban-plugin__tag-sort-input-wrapper,.kanban-plugin__tag-color-input-wrapper{display:flex;flex-direction:column;flex-grow:1;gap:1rem}.kanban-plugin__tag-sort-input-wrapper .kanban-plugin__setting-key-input-wrapper{margin-block-start:0}.kanban-plugin__tag-sort-input-wrapper .kanban-plugin__setting-input-wrapper{margin:0}.kanban-plugin__add-tag-color-button{align-self:baseline;margin:0}.kanban-plugin__date-color-wrapper,.kanban-plugin__tag-color-input .kanban-plugin__item-tags{background-color:var(--background-primary);padding:10px;margin:0;border-radius:4px}.kanban-plugin__tag-color-input .kanban-plugin__item-tag{margin-block-start:0;font-size:13px;font-weight:500;line-height:1.5}.kanban-plugin__date-color-input-wrapper input[type=number]{width:75px;padding-block:.6em;padding-inline:.8em;height:auto;border-radius:.5em}.kanban-plugin__date-color-input-wrapper .kanban-plugin__setting-item-label{margin-block-end:0}.kanban-plugin__date-color-config{padding-block:0 10px;padding-inline:0;display:flex;flex-wrap:wrap;gap:5px;align-items:center}.kanban-plugin__date-color-wrapper{display:inline-block;margin-block-start:10px}.kanban-plugin__date-color-wrapper .kanban-plugin__item-metadata{padding:0}.kanban-plugin__metadata-setting-desc{font-size:14px}.kanban-plugin__setting-button-spacer{visibility:hidden}.kanban-plugin__setting-item-label{font-size:12px;font-weight:700;margin-block-end:5px}.kanban-plugin__setting-toggle-wrapper .kanban-plugin__setting-item-label{margin-block-end:0}.kanban-plugin__hitbox{border:2px dashed tomato}.kanban-plugin__placeholder{flex-grow:0;flex-shrink:0;width:0;height:0;pointer-events:none}.kanban-plugin__placeholder[data-axis=horizontal]{height:100%}.kanban-plugin__placeholder[data-axis=vertical]{width:100%}body:not(.native-scrollbars) .kanban-plugin__scroll-container::-webkit-scrollbar{background-color:transparent;width:16px;height:16px}body:not(.native-scrollbars) .kanban-plugin__scroll-container::-webkit-scrollbar-thumb{border:4px solid transparent;background-clip:content-box}.kanban-plugin__scroll-container{will-change:transform}.kanban-plugin__scroll-container.kanban-plugin__horizontal{overflow-y:hidden;overflow-x:auto}.kanban-plugin__scroll-container.kanban-plugin__vertical{overflow-y:auto;overflow-x:hidden}.kanban-plugin__drag-container{contain:layout size;z-index:10000;pointer-events:none;position:fixed;top:0;left:0}.kanban-plugin__loading{width:100%;height:100%;display:flex;justify-content:center;align-items:center}.sk-pulse{width:60px;height:60px;background-color:var(--text-faint);border-radius:100%;animation:sk-pulse 1.2s infinite cubic-bezier(.455,.03,.515,.955)}@keyframes sk-pulse{0%{transform:scale(0)}to{transform:scale(1);opacity:0}}.kanban-plugin__color-picker-wrapper{position:relative}.kanban-plugin__color-picker{position:absolute;top:-5px;left:0;transform:translateY(-100%)}.kanban-plugin__date,.cm-kanban-time-wrapper,.cm-kanban-date-wrapper{display:inline-block;color:var(--date-color);border-radius:var(--radius-s);background-color:var(--date-background-color, rgba(var(--mono-rgb-100), .05))}.kanban-plugin__date:hover,.cm-kanban-time-wrapper:hover,.cm-kanban-date-wrapper:hover{background-color:var(--date-background-color, rgba(var(--mono-rgb-100), .1))}.kanban-plugin__date.kanban-plugin__preview-date-link,.cm-kanban-time-wrapper.kanban-plugin__preview-date-link,.cm-kanban-date-wrapper.kanban-plugin__preview-date-link{--link-decoration: none;--link-unresolved-decoration-style: unset}.kanban-plugin__date>span,.cm-kanban-time-wrapper>span,.cm-kanban-date-wrapper>span,.kanban-plugin__date>a,.cm-kanban-time-wrapper>a,.cm-kanban-date-wrapper>a{padding-inline:var(--size-2-1)}.completion .kanban-plugin__date.has-background{color:inherit;background-color:transparent}.completion .kanban-plugin__date.has-background:hover{background-color:transparent}.is-date .kanban-plugin__date:not(.has-background){background-color:transparent}.is-date .kanban-plugin__date:not(.has-background):hover{background-color:transparent}.kanban-plugin__meta-value .kanban-plugin__date:hover{background-color:var(--date-background-color, rgba(var(--mono-rgb-100), .05))}
 
.pandoc-plugin-error {
    color: red;
}
  body.css-settings-manager { } body.theme-light.css-settings-manager { } body.theme-dark.css-settings-manager { } .commit-msg-input.svelte-11adhly {width:100%;overflow:hidden;resize:none;padding:7px 5px;background-color:var(--background-modifier-form-field);}.git-commit-msg.svelte-11adhly {position:relative;padding:0;width:calc(100% - var(--size-4-8));margin:4px auto;}main.svelte-11adhly .git-tools:where(.svelte-11adhly) .files-count:where(.svelte-11adhly) {padding-left:var(--size-2-1);width:11px;display:flex;align-items:center;justify-content:center;}.nav-folder-title.svelte-11adhly {align-items:center;}.git-commit-msg-clear-button.svelte-11adhly {position:absolute;background:transparent;border-radius:50%;color:var(--search-clear-button-color);cursor:var(--cursor);top:-4px;right:2px;bottom:0px;line-height:0;height:var(--input-height);width:28px;margin:auto;padding:0 0;text-align:center;display:flex;justify-content:center;align-items:center;transition:color 0.15s ease-in-out;}.git-commit-msg-clear-button.svelte-11adhly:after {content:"";height:var(--search-clear-button-size);width:var(--search-clear-button-size);display:block;background-color:currentColor;mask-image:url("data:image/svg+xml,<svg viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12ZM3.8705 3.09766L6.00003 5.22718L8.12955 3.09766L8.9024 3.8705L6.77287 6.00003L8.9024 8.12955L8.12955 8.9024L6.00003 6.77287L3.8705 8.9024L3.09766 8.12955L5.22718 6.00003L3.09766 3.8705L3.8705 3.09766Z' fill='currentColor'/></svg>");mask-repeat:no-repeat;-webkit-mask-image:url("data:image/svg+xml,<svg viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12ZM3.8705 3.09766L6.00003 5.22718L8.12955 3.09766L8.9024 3.8705L6.77287 6.00003L8.9024 8.12955L8.12955 8.9024L6.00003 6.77287L3.8705 8.9024L3.09766 8.12955L5.22718 6.00003L3.09766 3.8705L3.8705 3.09766Z' fill='currentColor'/></svg>");-webkit-mask-repeat:no-repeat;} main.svelte-1wbh8tp .nav-file-title:where(.svelte-1wbh8tp) {align-items:center;}undefined
</style>
    </head>
    <body>
<h1 data-heading="CRUCA Office Procedures Manual" dir="auto">CRUCA Office Procedures Manual</h1>
<p dir="auto"><strong>Caboolture Region Uniting Church Australia</strong></p>
<hr>
<h2 data-heading="Table of Contents" dir="auto">Table of Contents</h2>
<h3 data-heading="Part 1: Introduction &amp; Overview" dir="auto">Part 1: Introduction &amp; Overview</h3>
<ul>
<li dir="auto"><a class="internal-link" data-href="#11-purpose-of-this-manual" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#11-purpose-of-this-manual" target="_blank" rel="noopener nofollow">1.1 Purpose of this Manual</a></li>
<li dir="auto"><a class="internal-link" data-href="#12-using-this-manual" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#12-using-this-manual" target="_blank" rel="noopener nofollow">1.2 Using this Manual</a></li>
<li dir="auto"><a class="internal-link" data-href="#13-key-roles--responsibilities" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#13-key-roles--responsibilities" target="_blank" rel="noopener nofollow">1.3 Key Roles &amp; Responsibilities</a></li>
<li dir="auto"><a class="internal-link" data-href="#14-office-hours--contact-information" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#14-office-hours--contact-information" target="_blank" rel="noopener nofollow">1.4 Office Hours &amp; Contact Information</a></li>
</ul>
<h3 data-heading="Part 2: Daily Operations" dir="auto">Part 2: Daily Operations</h3>
<ul>
<li dir="auto"><a class="internal-link" data-href="#21-opening--closing-procedures" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#21-opening--closing-procedures" target="_blank" rel="noopener nofollow">2.1 Opening &amp; Closing Procedures</a></li>
<li dir="auto"><a class="internal-link" data-href="#22-office-phone-system" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#22-office-phone-system" target="_blank" rel="noopener nofollow">2.2 Office Phone System</a></li>
<li dir="auto"><a class="internal-link" data-href="#23-visitor--enquiry-management" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#23-visitor--enquiry-management" target="_blank" rel="noopener nofollow">2.3 Visitor &amp; Enquiry Management</a></li>
<li dir="auto"><a class="internal-link" data-href="#24-mail-processing" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#24-mail-processing" target="_blank" rel="noopener nofollow">2.4 Mail Processing</a></li>
<li dir="auto"><a class="internal-link" data-href="#25-email-management" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#25-email-management" target="_blank" rel="noopener nofollow">2.5 Email Management</a></li>
</ul>
<h3 data-heading="Part 3: Financial Procedures" dir="auto">Part 3: Financial Procedures</h3>
<ul>
<li dir="auto"><a class="internal-link" data-href="#31-cash-handling" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#31-cash-handling" target="_blank" rel="noopener nofollow">3.1 Cash Handling</a></li>
<li dir="auto"><a class="internal-link" data-href="#32-receipting-money" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#32-receipting-money" target="_blank" rel="noopener nofollow">3.2 Receipting Money</a></li>
<li dir="auto"><a class="internal-link" data-href="#33-processing-accounts" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#33-processing-accounts" target="_blank" rel="noopener nofollow">3.3 Processing Accounts</a></li>
<li dir="auto"><a class="internal-link" data-href="#34-eftpos-operations" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#34-eftpos-operations" target="_blank" rel="noopener nofollow">3.4 EFTPOS Operations</a></li>
<li dir="auto"><a class="internal-link" data-href="#35-offertory-envelopes" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#35-offertory-envelopes" target="_blank" rel="noopener nofollow">3.5 Offertory Envelopes</a></li>
<li dir="auto"><a class="internal-link" data-href="#36-banking-procedures" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#36-banking-procedures" target="_blank" rel="noopener nofollow">3.6 Banking Procedures</a></li>
</ul>
<h3 data-heading="Part 4: Member Services" dir="auto">Part 4: Member Services</h3>
<ul>
<li dir="auto"><a class="internal-link" data-href="#41-new-member-registration" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#41-new-member-registration" target="_blank" rel="noopener nofollow">4.1 New Member Registration</a></li>
<li dir="auto"><a class="internal-link" data-href="#42-member-directory-management" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#42-member-directory-management" target="_blank" rel="noopener nofollow">4.2 Member Directory Management</a></li>
<li dir="auto"><a class="internal-link" data-href="#43-name-badge-orders" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#43-name-badge-orders" target="_blank" rel="noopener nofollow">4.3 Name Badge Orders</a></li>
<li dir="auto"><a class="internal-link" data-href="#44-welcome-cards" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#44-welcome-cards" target="_blank" rel="noopener nofollow">4.4 Welcome Cards</a></li>
<li dir="auto"><a class="internal-link" data-href="#45-baptism-procedures" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#45-baptism-procedures" target="_blank" rel="noopener nofollow">4.5 Baptism Procedures</a></li>
<li dir="auto"><a class="internal-link" data-href="#46-wedding-procedures" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#46-wedding-procedures" target="_blank" rel="noopener nofollow">4.6 Wedding Procedures</a></li>
<li dir="auto"><a class="internal-link" data-href="#47-funeral--memorial-services" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#47-funeral--memorial-services" target="_blank" rel="noopener nofollow">4.7 Funeral &amp; Memorial Services</a></li>
<li dir="auto"><a class="internal-link" data-href="#48-cemetery-enquiries" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#48-cemetery-enquiries" target="_blank" rel="noopener nofollow">4.8 Cemetery Enquiries</a></li>
</ul>
<h3 data-heading="Part 5: Communications" dir="auto">Part 5: Communications</h3>
<ul>
<li dir="auto"><a class="internal-link" data-href="#51-weekly-bulletin-preparation" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#51-weekly-bulletin-preparation" target="_blank" rel="noopener nofollow">5.1 Weekly Bulletin Preparation</a></li>
<li dir="auto"><a class="internal-link" data-href="#52-bulletin-distribution" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#52-bulletin-distribution" target="_blank" rel="noopener nofollow">5.2 Bulletin Distribution</a></li>
<li dir="auto"><a class="internal-link" data-href="#53-website-management" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#53-website-management" target="_blank" rel="noopener nofollow">5.3 Website Management</a></li>
<li dir="auto"><a class="internal-link" data-href="#54-social-media-guidelines" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#54-social-media-guidelines" target="_blank" rel="noopener nofollow">5.4 Social Media Guidelines</a></li>
<li dir="auto"><a class="internal-link" data-href="#55-mass-email-communications" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#55-mass-email-communications" target="_blank" rel="noopener nofollow">5.5 Mass Email Communications</a></li>
<li dir="auto"><a class="internal-link" data-href="#56-notice-board-management" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#56-notice-board-management" target="_blank" rel="noopener nofollow">5.6 Notice Board Management</a></li>
</ul>
<h3 data-heading="Part 6: Property &amp; Facilities" dir="auto">Part 6: Property &amp; Facilities</h3>
<ul>
<li dir="auto"><a class="internal-link" data-href="#61-hall-hire-procedures" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#61-hall-hire-procedures" target="_blank" rel="noopener nofollow">6.1 Hall Hire Procedures</a></li>
<li dir="auto"><a class="internal-link" data-href="#62-church-property-bookings" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#62-church-property-bookings" target="_blank" rel="noopener nofollow">6.2 Church Property Bookings</a></li>
<li dir="auto"><a class="internal-link" data-href="#63-keys--security" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#63-keys--security" target="_blank" rel="noopener nofollow">6.3 Keys &amp; Security</a></li>
<li dir="auto"><a class="internal-link" data-href="#64-equipment-maintenance" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#64-equipment-maintenance" target="_blank" rel="noopener nofollow">6.4 Equipment Maintenance</a></li>
<li dir="auto"><a class="internal-link" data-href="#65-cleaning-schedules" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#65-cleaning-schedules" target="_blank" rel="noopener nofollow">6.5 Cleaning Schedules</a></li>
</ul>
<h3 data-heading="Part 7: Volunteer Management" dir="auto">Part 7: Volunteer Management</h3>
<ul>
<li dir="auto"><a class="internal-link" data-href="#71-volunteer-registration" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#71-volunteer-registration" target="_blank" rel="noopener nofollow">7.1 Volunteer Registration</a></li>
<li dir="auto"><a class="internal-link" data-href="#72-volunteer-rights--responsibilities" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#72-volunteer-rights--responsibilities" target="_blank" rel="noopener nofollow">7.2 Volunteer Rights &amp; Responsibilities</a></li>
<li dir="auto"><a class="internal-link" data-href="#73-roster-management" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#73-roster-management" target="_blank" rel="noopener nofollow">7.3 Roster Management</a></li>
<li dir="auto"><a class="internal-link" data-href="#74-training--induction" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#74-training--induction" target="_blank" rel="noopener nofollow">7.4 Training &amp; Induction</a></li>
</ul>
<h3 data-heading="Part 8: Safe Church Compliance" dir="auto">Part 8: Safe Church Compliance</h3>
<ul>
<li dir="auto"><a class="internal-link" data-href="#81-blue-card-requirements" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#81-blue-card-requirements" target="_blank" rel="noopener nofollow">8.1 Blue Card Requirements</a></li>
<li dir="auto"><a class="internal-link" data-href="#82-blue-card-reporting" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#82-blue-card-reporting" target="_blank" rel="noopener nofollow">8.2 Blue Card Reporting</a></li>
<li dir="auto"><a class="internal-link" data-href="#83-safe-church-training" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#83-safe-church-training" target="_blank" rel="noopener nofollow">8.3 Safe Church Training</a></li>
<li dir="auto"><a class="internal-link" data-href="#84-incident-reporting" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#84-incident-reporting" target="_blank" rel="noopener nofollow">8.4 Incident Reporting</a></li>
<li dir="auto"><a class="internal-link" data-href="#85-privacy--confidentiality" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#85-privacy--confidentiality" target="_blank" rel="noopener nofollow">8.5 Privacy &amp; Confidentiality</a></li>
</ul>
<h3 data-heading="Part 9: Administration Systems" dir="auto">Part 9: Administration Systems</h3>
<ul>
<li dir="auto"><a class="internal-link" data-href="#91-database-management-pastoral-care" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#91-database-management-pastoral-care" target="_blank" rel="noopener nofollow">9.1 Database Management (Pastoral Care)</a></li>
<li dir="auto"><a class="internal-link" data-href="#92-document-filing-system" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#92-document-filing-system" target="_blank" rel="noopener nofollow">9.2 Document Filing System</a></li>
<li dir="auto"><a class="internal-link" data-href="#93-annual-reports" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#93-annual-reports" target="_blank" rel="noopener nofollow">9.3 Annual Reports</a></li>
<li dir="auto"><a class="internal-link" data-href="#94-monthly-reporting" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#94-monthly-reporting" target="_blank" rel="noopener nofollow">9.4 Monthly Reporting</a></li>
<li dir="auto"><a class="internal-link" data-href="#95-archiving-procedures" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#95-archiving-procedures" target="_blank" rel="noopener nofollow">9.5 Archiving Procedures</a></li>
</ul>
<h3 data-heading="Part 10: Health &amp; Safety" dir="auto">Part 10: Health &amp; Safety</h3>
<ul>
<li dir="auto"><a class="internal-link" data-href="#101-fire-evacuation-procedures" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#101-fire-evacuation-procedures" target="_blank" rel="noopener nofollow">10.1 Fire Evacuation Procedures</a></li>
<li dir="auto"><a class="internal-link" data-href="#102-fire-equipment-testing" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#102-fire-equipment-testing" target="_blank" rel="noopener nofollow">10.2 Fire Equipment Testing</a></li>
<li dir="auto"><a class="internal-link" data-href="#103-first-aid-procedures" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#103-first-aid-procedures" target="_blank" rel="noopener nofollow">10.3 First Aid Procedures</a></li>
<li dir="auto"><a class="internal-link" data-href="#104-emergency-contacts" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#104-emergency-contacts" target="_blank" rel="noopener nofollow">10.4 Emergency Contacts</a></li>
<li dir="auto"><a class="internal-link" data-href="#105-workplace-health--safety" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#105-workplace-health--safety" target="_blank" rel="noopener nofollow">10.5 Workplace Health &amp; Safety</a></li>
</ul>
<h3 data-heading="Part 11: Resources &amp; Supplies" dir="auto">Part 11: Resources &amp; Supplies</h3>
<ul>
<li dir="auto"><a class="internal-link" data-href="#111-stationery-ordering" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#111-stationery-ordering" target="_blank" rel="noopener nofollow">11.1 Stationery Ordering</a></li>
<li dir="auto"><a class="internal-link" data-href="#112-kitchen-supplies" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#112-kitchen-supplies" target="_blank" rel="noopener nofollow">11.2 Kitchen Supplies</a></li>
<li dir="auto"><a class="internal-link" data-href="#113-cleaning-supplies" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#113-cleaning-supplies" target="_blank" rel="noopener nofollow">11.3 Cleaning Supplies</a></li>
<li dir="auto"><a class="internal-link" data-href="#114-gas-bottle-replacement" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#114-gas-bottle-replacement" target="_blank" rel="noopener nofollow">11.4 Gas Bottle Replacement</a></li>
<li dir="auto"><a class="internal-link" data-href="#115-printer--copier-supplies" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#115-printer--copier-supplies" target="_blank" rel="noopener nofollow">11.5 Printer &amp; Copier Supplies</a></li>
</ul>
<h3 data-heading="Part 12: Technology &amp; Equipment" dir="auto">Part 12: Technology &amp; Equipment</h3>
<ul>
<li dir="auto"><a class="internal-link" data-href="#121-computer-systems" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#121-computer-systems" target="_blank" rel="noopener nofollow">12.1 Computer Systems</a></li>
<li dir="auto"><a class="internal-link" data-href="#122-printer--copier-operations" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#122-printer--copier-operations" target="_blank" rel="noopener nofollow">12.2 Printer &amp; Copier Operations</a></li>
<li dir="auto"><a class="internal-link" data-href="#123-audio-visual-equipment" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#123-audio-visual-equipment" target="_blank" rel="noopener nofollow">12.3 Audio-Visual Equipment</a></li>
<li dir="auto"><a class="internal-link" data-href="#124-backup-procedures" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#124-backup-procedures" target="_blank" rel="noopener nofollow">12.4 Backup Procedures</a></li>
<li dir="auto"><a class="internal-link" data-href="#125-it-support" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#125-it-support" target="_blank" rel="noopener nofollow">12.5 IT Support</a></li>
</ul>
<h3 data-heading="Appendices" dir="auto">Appendices</h3>
<ul>
<li dir="auto"><a class="internal-link" data-href="#appendix-a-forms--templates" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#appendix-a-forms--templates" target="_blank" rel="noopener nofollow">Appendix A: Forms &amp; Templates</a></li>
<li dir="auto"><a class="internal-link" data-href="#appendix-b-contact-lists" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#appendix-b-contact-lists" target="_blank" rel="noopener nofollow">Appendix B: Contact Lists</a></li>
<li dir="auto"><a class="internal-link" data-href="#appendix-c-quick-reference-guides" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#appendix-c-quick-reference-guides" target="_blank" rel="noopener nofollow">Appendix C: Quick Reference Guides</a></li>
<li dir="auto"><a class="internal-link" data-href="#appendix-d-troubleshooting-common-issues" href="C:\Users\<USER>\obsidian-vaults\cruca-docs\cruca-documentation-git\Office-Procedures\index.html#appendix-d-troubleshooting-common-issues" target="_blank" rel="noopener nofollow">Appendix D: Troubleshooting Common Issues</a></li>
</ul>
<hr>
<h2 data-heading="Part 1: Introduction &amp; Overview" dir="auto">Part 1: Introduction &amp; Overview</h2>
<h3 data-heading="1.1 Purpose of this Manual" dir="auto">1.1 Purpose of this Manual</h3>
<p dir="auto">This Office Procedures Manual serves as the comprehensive guide for all administrative operations at Caboolture Region Uniting Church Australia (CRUCA). It provides standardized procedures to ensure consistent, efficient, and professional administration of church operations.</p>
<p dir="auto"><strong>Key Objectives:</strong></p>
<ul>
<li dir="auto">Provide clear, step-by-step procedures for all office tasks</li>
<li dir="auto">Ensure continuity of operations regardless of staff changes</li>
<li dir="auto">Maintain compliance with church policies and legal requirements</li>
<li dir="auto">Support volunteers and staff in their administrative roles</li>
</ul>
<h3 data-heading="1.2 Using this Manual" dir="auto">1.2 Using this Manual</h3>
<p dir="auto">This manual is designed for:</p>
<ul>
<li dir="auto"><strong>Church Administrator</strong>: Primary responsibility for office operations</li>
<li dir="auto"><strong>Office Volunteers</strong>: Supporting administrative tasks</li>
<li dir="auto"><strong>Ministry Team</strong>: Understanding office procedures and systems</li>
<li dir="auto"><strong>Church Council</strong>: Oversight and governance reference</li>
</ul>
<p dir="auto"><strong>Navigation Tips:</strong></p>
<ul>
<li dir="auto">Use the Table of Contents to find specific procedures</li>
<li dir="auto">Each procedure includes Purpose, Requirements, and Steps</li>
<li dir="auto">Look for "Important Notes" boxes for critical information</li>
<li dir="auto">Check Appendices for forms and quick references</li>
</ul>
<h3 data-heading="1.3 Key Roles &amp; Responsibilities" dir="auto">1.3 Key Roles &amp; Responsibilities</h3>
<h4 data-heading="Church Administrator" dir="auto">Church Administrator</h4>
<ul>
<li dir="auto">Overall office management and coordination</li>
<li dir="auto">Financial record keeping and reporting</li>
<li dir="auto">Member database management</li>
<li dir="auto">Communication coordination</li>
<li dir="auto">Compliance oversight</li>
</ul>
<h4 data-heading="Office Volunteers" dir="auto">Office Volunteers</h4>
<ul>
<li dir="auto">Reception and phone duties</li>
<li dir="auto">Basic data entry</li>
<li dir="auto">Bulletin preparation assistance</li>
<li dir="auto">Mail and filing support</li>
<li dir="auto">General administrative tasks</li>
</ul>
<h4 data-heading="Ministry Team Leader" dir="auto">Ministry Team Leader</h4>
<ul>
<li dir="auto">Pastoral oversight</li>
<li dir="auto">Service planning coordination</li>
<li dir="auto">Member care direction</li>
<li dir="auto">Community engagement</li>
</ul>
<h4 data-heading="Treasurer" dir="auto">Treasurer</h4>
<ul>
<li dir="auto">Financial oversight</li>
<li dir="auto">Banking authorization</li>
<li dir="auto">Budget management</li>
<li dir="auto">Financial reporting</li>
</ul>
<h4 data-heading="Property Officer" dir="auto">Property Officer</h4>
<ul>
<li dir="auto">Facility management</li>
<li dir="auto">Maintenance coordination</li>
<li dir="auto">Hall hire oversight</li>
<li dir="auto">Security management</li>
</ul>
<h3 data-heading="1.4 Office Hours &amp; Contact Information" dir="auto">1.4 Office Hours &amp; Contact Information</h3>
<p dir="auto"><strong>Standard Office Hours:</strong></p>
<ul>
<li dir="auto">Monday to Friday: 9:00 AM - 2:00 PM</li>
<li dir="auto">Closed: Public Holidays and designated closure periods</li>
</ul>
<p dir="auto"><strong>Contact Information:</strong></p>
<ul>
<li dir="auto">Main Office Phone: [To be maintained separately]</li>
<li dir="auto">Email: office@[church-domain]</li>
<li dir="auto">Physical Address: [To be maintained separately]</li>
<li dir="auto">Postal Address: [To be maintained separately]</li>
</ul>
<blockquote dir="auto">
<p><strong>Important Note:</strong> Specific contact details are maintained separately to ensure this manual remains current. Refer to the Contact Information Sheet for current details.</p>
</blockquote>
<hr>
<h2 data-heading="Part 2: Daily Operations" dir="auto">Part 2: Daily Operations</h2>
<h3 data-heading="2.1 Opening &amp; Closing Procedures" dir="auto">2.1 Opening &amp; Closing Procedures</h3>
<h4 data-heading="Opening Procedures" dir="auto">Opening Procedures</h4>
<p dir="auto"><strong>Time:</strong> 15 minutes before official opening</p>
<p dir="auto"><strong>Steps:</strong></p>
<ol>
<li dir="auto">
<p><strong>Security Check</strong></p>
<ul>
<li dir="auto">Disable alarm system</li>
<li dir="auto">Check all doors and windows</li>
<li dir="auto">Note any security concerns in log book</li>
</ul>
</li>
<li dir="auto">
<p><strong>Systems Activation</strong></p>
<ul>
<li dir="auto">Turn on computers and printers</li>
<li dir="auto">Check answering machine for messages</li>
<li dir="auto">Log into church management system</li>
<li dir="auto">Open email client</li>
</ul>
</li>
<li dir="auto">
<p><strong>Preparation</strong></p>
<ul>
<li dir="auto">Check diary for day's appointments</li>
<li dir="auto">Review task list</li>
<li dir="auto">Prepare visitor sign-in sheet</li>
<li dir="auto">Ensure reception area is tidy</li>
</ul>
</li>
<li dir="auto">
<p><strong>Communication Check</strong></p>
<ul>
<li dir="auto">Review overnight emails</li>
<li dir="auto">Check for urgent messages</li>
<li dir="auto">Update staff availability board</li>
</ul>
</li>
</ol>
<h4 data-heading="Closing Procedures" dir="auto">Closing Procedures</h4>
<p dir="auto"><strong>Time:</strong> Allow 15 minutes after official closing</p>
<p dir="auto"><strong>Steps:</strong></p>
<ol>
<li dir="auto">
<p><strong>Secure Information</strong></p>
<ul>
<li dir="auto">Lock filing cabinets</li>
<li dir="auto">Secure cash box</li>
<li dir="auto">Clear desks of sensitive documents</li>
</ul>
</li>
<li dir="auto">
<p><strong>Systems Shutdown</strong></p>
<ul>
<li dir="auto">Save and close all programs</li>
<li dir="auto">Backup daily work (if applicable)</li>
<li dir="auto">Shut down computers</li>
<li dir="auto">Turn off printers and copiers</li>
</ul>
</li>
<li dir="auto">
<p><strong>Facility Security</strong></p>
<ul>
<li dir="auto">Check all windows are locked</li>
<li dir="auto">Turn off lights</li>
<li dir="auto">Set answering machine</li>
<li dir="auto">Activate alarm system</li>
<li dir="auto">Lock all doors</li>
</ul>
</li>
<li dir="auto">
<p><strong>Final Checks</strong></p>
<ul>
<li dir="auto">Ensure all appliances are off</li>
<li dir="auto">Check bathrooms and meeting rooms</li>
<li dir="auto">Complete daily log</li>
</ul>
</li>
</ol>
<h3 data-heading="2.2 Office Phone System" dir="auto">2.2 Office Phone System</h3>
<h4 data-heading="Answering Calls" dir="auto">Answering Calls</h4>
<p dir="auto"><strong>Standard Greeting:</strong><br>
"Good [morning/afternoon], Caboolture Uniting Church, [your name] speaking. How may I help you?"</p>
<p dir="auto"><strong>Call Handling Procedures:</strong></p>
<ol>
<li dir="auto">
<p><strong>General Enquiries</strong></p>
<ul>
<li dir="auto">Provide information clearly</li>
<li dir="auto">Offer to email additional details</li>
<li dir="auto">Log enquiry in visitor register</li>
</ul>
</li>
<li dir="auto">
<p><strong>Ministry Team Requests</strong></p>
<ul>
<li dir="auto">Check availability calendar</li>
<li dir="auto">Take detailed message if unavailable</li>
<li dir="auto">Use message form (Appendix A)</li>
</ul>
</li>
<li dir="auto">
<p><strong>Urgent/Pastoral Care</strong></p>
<ul>
<li dir="auto">Obtain contact details immediately</li>
<li dir="auto">Assess urgency level</li>
<li dir="auto">Follow pastoral care protocol</li>
<li dir="auto">Contact ministry team if required</li>
</ul>
</li>
<li dir="auto">
<p><strong>Complaints</strong></p>
<ul>
<li dir="auto">Listen without interrupting</li>
<li dir="auto">Document details</li>
<li dir="auto">Advise of follow-up process</li>
<li dir="auto">Refer to appropriate person</li>
</ul>
</li>
</ol>
<p dir="auto"><strong>Voicemail Management:</strong></p>
<ul>
<li dir="auto">Check messages every 2 hours</li>
<li dir="auto">Transcribe to message forms</li>
<li dir="auto">Distribute to relevant persons</li>
<li dir="auto">Mark messages as actioned</li>
</ul>
<h3 data-heading="2.3 Visitor &amp; Enquiry Management" dir="auto">2.3 Visitor &amp; Enquiry Management</h3>
<h4 data-heading="Reception Procedures" dir="auto">Reception Procedures</h4>
<ol>
<li dir="auto">
<p><strong>Greeting Visitors</strong></p>
<ul>
<li dir="auto">Stand and greet warmly</li>
<li dir="auto">Ask how you can help</li>
<li dir="auto">Offer seating if waiting required</li>
</ul>
</li>
<li dir="auto">
<p><strong>Visitor Registration</strong></p>
<ul>
<li dir="auto">All visitors sign visitor book</li>
<li dir="auto">Note purpose of visit</li>
<li dir="auto">Provide visitor badge if required</li>
</ul>
</li>
<li dir="auto">
<p><strong>Common Enquiries</strong></p>
<p><strong>Hall Hire:</strong></p>
<ul>
<li dir="auto">Provide information pack</li>
<li dir="auto">Show calendar availability</li>
<li dir="auto">Book viewing if requested</li>
<li dir="auto">Refer to Property Officer for confirmation</li>
</ul>
<p><strong>Baptism/Wedding:</strong></p>
<ul>
<li dir="auto">Provide information pack</li>
<li dir="auto">Check ministry team availability</li>
<li dir="auto">Book initial consultation</li>
<li dir="auto">Create preliminary file</li>
</ul>
<p><strong>Membership:</strong></p>
<ul>
<li dir="auto">Provide welcome pack</li>
<li dir="auto">Explain membership process</li>
<li dir="auto">Schedule meeting with ministry team</li>
<li dir="auto">Add to database as "enquirer"</li>
</ul>
</li>
<li dir="auto">
<p><strong>Security Considerations</strong></p>
<ul>
<li dir="auto">Never leave visitors unattended in office</li>
<li dir="auto">Secure sensitive areas</li>
<li dir="auto">Report suspicious behavior</li>
<li dir="auto">Follow child safety protocols</li>
</ul>
</li>
</ol>
<h3 data-heading="2.4 Mail Processing" dir="auto">2.4 Mail Processing</h3>
<h4 data-heading="Incoming Mail" dir="auto">Incoming Mail</h4>
<p dir="auto"><strong>Daily Process (10:00 AM):</strong></p>
<ol>
<li dir="auto">
<p><strong>Collection</strong></p>
<ul>
<li dir="auto">Collect from mailbox</li>
<li dir="auto">Sort by category</li>
<li dir="auto">Date stamp all items</li>
</ul>
</li>
<li dir="auto">
<p><strong>Categories:</strong></p>
<ul>
<li dir="auto"><strong>Urgent</strong>: Ministry team, treasurer</li>
<li dir="auto"><strong>Accounts</strong>: Treasurer folder</li>
<li dir="auto"><strong>General</strong>: Administrator review</li>
<li dir="auto"><strong>Publications</strong>: Reading table</li>
<li dir="auto"><strong>Member mail</strong>: Member pigeonholes</li>
</ul>
</li>
<li dir="auto">
<p><strong>Distribution</strong></p>
<ul>
<li dir="auto">Place in appropriate trays</li>
<li dir="auto">Email notification for urgent items</li>
<li dir="auto">Log registered mail</li>
</ul>
</li>
</ol>
<h4 data-heading="Outgoing Mail" dir="auto">Outgoing Mail</h4>
<p dir="auto"><strong>Daily Process (1:00 PM):</strong></p>
<ol>
<li dir="auto">
<p><strong>Preparation</strong></p>
<ul>
<li dir="auto">Check all items are sealed</li>
<li dir="auto">Verify addresses</li>
<li dir="auto">Apply correct postage</li>
<li dir="auto">Frank if applicable</li>
</ul>
</li>
<li dir="auto">
<p><strong>Recording</strong></p>
<ul>
<li dir="auto">Log registered items</li>
<li dir="auto">Record bulk mailings</li>
<li dir="auto">Note postage costs</li>
</ul>
</li>
<li dir="auto">
<p><strong>Dispatch</strong></p>
<ul>
<li dir="auto">Post by 2:00 PM for same-day processing</li>
<li dir="auto">Use express post for urgent items</li>
<li dir="auto">Arrange courier for packages</li>
</ul>
</li>
</ol>
<h3 data-heading="2.5 Email Management" dir="auto">2.5 Email Management</h3>
<h4 data-heading="Email Accounts" dir="auto">Email Accounts</h4>
<p dir="auto"><strong>Main Office Account:</strong></p>
<ul>
<li dir="auto">Check every hour during office hours</li>
<li dir="auto">Respond within 24 hours</li>
<li dir="auto">Use standard signatures</li>
<li dir="auto">File in appropriate folders</li>
</ul>
<p dir="auto"><strong>Email Categories &amp; Actions:</strong></p>
<ol>
<li dir="auto">
<p><strong>General Enquiries</strong></p>
<ul>
<li dir="auto">Respond with standard information</li>
<li dir="auto">Add to mailing list if requested</li>
<li dir="auto">File in "Enquiries" folder</li>
</ul>
</li>
<li dir="auto">
<p><strong>Member Communications</strong></p>
<ul>
<li dir="auto">Forward to relevant persons</li>
<li dir="auto">Update member records</li>
<li dir="auto">File in member folders</li>
</ul>
</li>
<li dir="auto">
<p><strong>Accounts/Financial</strong></p>
<ul>
<li dir="auto">Forward to treasurer</li>
<li dir="auto">Print if required for processing</li>
<li dir="auto">File in "Finance" folder</li>
</ul>
</li>
<li dir="auto">
<p><strong>Booking Requests</strong></p>
<ul>
<li dir="auto">Check availability</li>
<li dir="auto">Send confirmation/decline</li>
<li dir="auto">Update booking calendar</li>
<li dir="auto">File in "Bookings" folder</li>
</ul>
</li>
</ol>
<p dir="auto"><strong>Email Etiquette:</strong></p>
<ul>
<li dir="auto">Professional tone always</li>
<li dir="auto">Proofread before sending</li>
<li dir="auto">Use BCC for group emails</li>
<li dir="auto">Include clear subject lines</li>
<li dir="auto">Add disclaimer footer</li>
</ul>
<hr>
<h2 data-heading="Part 3: Financial Procedures" dir="auto">Part 3: Financial Procedures</h2>
<h3 data-heading="3.1 Cash Handling" dir="auto">3.1 Cash Handling</h3>
<h4 data-heading="General Principles" dir="auto">General Principles</h4>
<p dir="auto"><strong>Security Requirements:</strong></p>
<ul>
<li dir="auto">Never leave cash unattended</li>
<li dir="auto">Two-person verification for counts over $500</li>
<li dir="auto">Immediate banking of amounts over $1,000</li>
<li dir="auto">Secure storage in locked cash box/safe</li>
</ul>
<h4 data-heading="Daily Cash Procedures" dir="auto">Daily Cash Procedures</h4>
<ol>
<li dir="auto">
<p><strong>Receiving Cash</strong></p>
<ul>
<li dir="auto">Count in presence of payer</li>
<li dir="auto">Issue receipt immediately</li>
<li dir="auto">Record in cash receipt book</li>
<li dir="auto">Secure in cash box</li>
</ul>
</li>
<li dir="auto">
<p><strong>Cash Box Management</strong></p>
<ul>
<li dir="auto">Starting float: $200</li>
<li dir="auto">Maximum holding: $500</li>
<li dir="auto">Count and reconcile daily</li>
<li dir="auto">Document all transactions</li>
</ul>
</li>
<li dir="auto">
<p><strong>Petty Cash</strong></p>
<ul>
<li dir="auto">Maximum claim: $50</li>
<li dir="auto">Require receipt/documentation</li>
<li dir="auto">Record in petty cash book</li>
<li dir="auto">Reconcile weekly</li>
</ul>
</li>
</ol>
<h3 data-heading="3.2 Receipting Money" dir="auto">3.2 Receipting Money</h3>
<h4 data-heading="Receipt Requirements" dir="auto">Receipt Requirements</h4>
<p dir="auto"><strong>All receipts must include:</strong></p>
<ul>
<li dir="auto">Date and receipt number</li>
<li dir="auto">Payer name</li>
<li dir="auto">Amount in words and figures</li>
<li dir="auto">Purpose of payment</li>
<li dir="auto">Receiver signature</li>
</ul>
<h4 data-heading="Types of Receipts" dir="auto">Types of Receipts</h4>
<ol>
<li dir="auto">
<p><strong>Donations</strong></p>
<ul>
<li dir="auto">Use official donation receipt</li>
<li dir="auto">Note if tax-deductible</li>
<li dir="auto">Record envelope number if applicable</li>
<li dir="auto">File copy for annual statements</li>
</ul>
</li>
<li dir="auto">
<p><strong>Hall Hire</strong></p>
<ul>
<li dir="auto">Issue from hall hire receipt book</li>
<li dir="auto">Note booking date and details</li>
<li dir="auto">Indicate if deposit or full payment</li>
<li dir="auto">Attach to booking form</li>
</ul>
</li>
<li dir="auto">
<p><strong>Event Payments</strong></p>
<ul>
<li dir="auto">Use event-specific receipts</li>
<li dir="auto">List what payment covers</li>
<li dir="auto">Note any dietary requirements</li>
<li dir="auto">File with event records</li>
</ul>
</li>
</ol>
<h4 data-heading="Electronic Receipts" dir="auto">Electronic Receipts</h4>
<p dir="auto"><strong>For EFTPOS/Online payments:</strong></p>
<ul>
<li dir="auto">Print transaction record</li>
<li dir="auto">Attach to manual receipt</li>
<li dir="auto">Email copy to payer</li>
<li dir="auto">File in daily banking</li>
</ul>
<h3 data-heading="3.3 Processing Accounts" dir="auto">3.3 Processing Accounts</h3>
<h4 data-heading="Incoming Invoices" dir="auto">Incoming Invoices</h4>
<p dir="auto"><strong>Weekly Process (Thursdays):</strong></p>
<ol>
<li dir="auto">
<p><strong>Receipt &amp; Recording</strong></p>
<ul>
<li dir="auto">Date stamp on receipt</li>
<li dir="auto">Check for approval signature</li>
<li dir="auto">Enter in accounts register</li>
<li dir="auto">Code to appropriate budget line</li>
</ul>
</li>
<li dir="auto">
<p><strong>Approval Process</strong></p>
<ul>
<li dir="auto">Under $500: Administrator approval</li>
<li dir="auto">$500-$2000: Ministry Team Leader</li>
<li dir="auto">Over $2000: Treasurer approval</li>
<li dir="auto">Capital items: Church Council approval</li>
</ul>
</li>
<li dir="auto">
<p><strong>Payment Preparation</strong></p>
<ul>
<li dir="auto">Prepare payment list</li>
<li dir="auto">Generate payment authorizations</li>
<li dir="auto">Submit to treasurer by Friday</li>
</ul>
</li>
<li dir="auto">
<p><strong>Filing</strong></p>
<ul>
<li dir="auto">File paid invoices by month</li>
<li dir="auto">Maintain supplier files</li>
<li dir="auto">Keep tax invoices for 7 years</li>
</ul>
</li>
</ol>
<h4 data-heading="Payment Methods" dir="auto">Payment Methods</h4>
<p dir="auto"><strong>Electronic Funds Transfer (Preferred):</strong></p>
<ul>
<li dir="auto">Verify BSB and account numbers</li>
<li dir="auto">Use supplier reference numbers</li>
<li dir="auto">Email remittance advice</li>
<li dir="auto">File confirmation</li>
</ul>
<p dir="auto"><strong>Cheque (If required):</strong></p>
<ul>
<li dir="auto">Two signatures required</li>
<li dir="auto">Cross "Not Negotiable"</li>
<li dir="auto">Record in cheque register</li>
<li dir="auto">Mail with remittance</li>
</ul>
<h3 data-heading="3.4 EFTPOS Operations" dir="auto">3.4 EFTPOS Operations</h3>
<h4 data-heading="Daily Operations" dir="auto">Daily Operations</h4>
<ol>
<li dir="auto">
<p><strong>Starting Procedures</strong></p>
<ul>
<li dir="auto">Check terminal connection</li>
<li dir="auto">Print start-of-day report</li>
<li dir="auto">Verify float amount</li>
<li dir="auto">Test with $0.01 transaction (void after)</li>
</ul>
</li>
<li dir="auto">
<p><strong>Transaction Processing</strong></p>
<ul>
<li dir="auto">Enter amount carefully</li>
<li dir="auto">Customer enters PIN</li>
<li dir="auto">Wait for approval</li>
<li dir="auto">Print customer receipt</li>
<li dir="auto">Print merchant copy</li>
</ul>
</li>
<li dir="auto">
<p><strong>End of Day</strong></p>
<ul>
<li dir="auto">Perform settlement</li>
<li dir="auto">Print settlement report</li>
<li dir="auto">Reconcile with receipts</li>
<li dir="auto">File reports</li>
</ul>
</li>
</ol>
<h4 data-heading="Troubleshooting" dir="auto">Troubleshooting</h4>
<p dir="auto"><strong>Common Issues:</strong></p>
<p dir="auto"><strong>"Transaction Declined"</strong></p>
<ul>
<li dir="auto">Try alternative payment method</li>
<li dir="auto">Check card expiry</li>
<li dir="auto">Contact bank if persistent</li>
</ul>
<p dir="auto"><strong>"Connection Error"</strong></p>
<ul>
<li dir="auto">Check cables and power</li>
<li dir="auto">Restart terminal</li>
<li dir="auto">Use manual backup if needed</li>
<li dir="auto">Contact support if unresolved</li>
</ul>
<h3 data-heading="3.5 Offertory Envelopes" dir="auto">3.5 Offertory Envelopes</h3>
<h4 data-heading="Annual Distribution" dir="auto">Annual Distribution</h4>
<p dir="auto"><strong>Timeline (November-December):</strong></p>
<ol>
<li dir="auto"><strong>Week 1</strong>: Generate member list</li>
<li dir="auto"><strong>Week 2</strong>: Order envelopes</li>
<li dir="auto"><strong>Week 3</strong>: Prepare distribution list</li>
<li dir="auto"><strong>Week 4</strong>: Package for collection</li>
<li dir="auto"><strong>January</strong>: Distribute at services</li>
</ol>
<h4 data-heading="Weekly Processing" dir="auto">Weekly Processing</h4>
<p dir="auto"><strong>After Each Service:</strong></p>
<ol>
<li dir="auto">
<p><strong>Collection</strong></p>
<ul>
<li dir="auto">Secure immediately after service</li>
<li dir="auto">Two people present for counting</li>
</ul>
</li>
<li dir="auto">
<p><strong>Recording</strong></p>
<ul>
<li dir="auto">Open envelopes carefully</li>
<li dir="auto">Record amount and envelope number</li>
<li dir="auto">Note any special designations</li>
<li dir="auto">Balance with cash count</li>
</ul>
</li>
<li dir="auto">
<p><strong>Data Entry</strong></p>
<ul>
<li dir="auto">Enter in donation system</li>
<li dir="auto">Print summary report</li>
<li dir="auto">File envelopes by date</li>
</ul>
</li>
</ol>
<h4 data-heading="Annual Statements" dir="auto">Annual Statements</h4>
<p dir="auto"><strong>Process (January):</strong></p>
<ol>
<li dir="auto">Generate statements from system</li>
<li dir="auto">Review for accuracy</li>
<li dir="auto">Print on church letterhead</li>
<li dir="auto">Mail by January 31st</li>
<li dir="auto">File copies</li>
</ol>
<h3 data-heading="3.6 Banking Procedures" dir="auto">3.6 Banking Procedures</h3>
<h4 data-heading="Weekly Banking" dir="auto">Weekly Banking</h4>
<p dir="auto"><strong>Schedule:</strong> Mondays and Thursdays</p>
<p dir="auto"><strong>Preparation:</strong></p>
<ol>
<li dir="auto">
<p><strong>Count &amp; Reconcile</strong></p>
<ul>
<li dir="auto">Count all cash and cheques</li>
<li dir="auto">Complete deposit slip</li>
<li dir="auto">Reconcile with receipts</li>
<li dir="auto">Photocopy cheques</li>
</ul>
</li>
<li dir="auto">
<p><strong>Security</strong></p>
<ul>
<li dir="auto">Use locked bank bag</li>
<li dir="auto">Vary route and timing</li>
<li dir="auto">Never bank alone with large amounts</li>
<li dir="auto">Use night safe if necessary</li>
</ul>
</li>
<li dir="auto">
<p><strong>Documentation</strong></p>
<ul>
<li dir="auto">File deposit slip</li>
<li dir="auto">Record in banking register</li>
<li dir="auto">Attach receipt copies</li>
<li dir="auto">Update financial records</li>
</ul>
</li>
</ol>
<h4 data-heading="Online Banking" dir="auto">Online Banking</h4>
<p dir="auto"><strong>Daily Tasks:</strong></p>
<ul>
<li dir="auto">Check account balances</li>
<li dir="auto">Verify deposits received</li>
<li dir="auto">Review pending transactions</li>
<li dir="auto">Download statements</li>
</ul>
<p dir="auto"><strong>Weekly Tasks:</strong></p>
<ul>
<li dir="auto">Reconcile accounts</li>
<li dir="auto">Process transfers</li>
<li dir="auto">Pay approved invoices</li>
<li dir="auto">Archive statements</li>
</ul>
<hr>
<h2 data-heading="Part 4: Member Services" dir="auto">Part 4: Member Services</h2>
<h3 data-heading="4.1 New Member Registration" dir="auto">4.1 New Member Registration</h3>
<h4 data-heading="Initial Contact" dir="auto">Initial Contact</h4>
<ol>
<li dir="auto">
<p><strong>Information Gathering</strong></p>
<ul>
<li dir="auto">Full name and preferred name</li>
<li dir="auto">Contact details (phone, email, address)</li>
<li dir="auto">Previous church (if applicable)</li>
<li dir="auto">Family members</li>
<li dir="auto">Emergency contact</li>
</ul>
</li>
<li dir="auto">
<p><strong>Welcome Pack</strong></p>
<ul>
<li dir="auto">Church information booklet</li>
<li dir="auto">Service times and activities</li>
<li dir="auto">Membership information</li>
<li dir="auto">Ministry team introduction</li>
<li dir="auto">Volunteer opportunities</li>
</ul>
</li>
<li dir="auto">
<p><strong>Database Entry</strong></p>
<ul>
<li dir="auto">Create new record</li>
<li dir="auto">Assign member number</li>
<li dir="auto">Set status as "Visitor/Enquirer"</li>
<li dir="auto">Add to mailing lists</li>
</ul>
</li>
</ol>
<h4 data-heading="Membership Process" dir="auto">Membership Process</h4>
<p dir="auto"><strong>Steps to Membership:</strong></p>
<ol>
<li dir="auto">
<p><strong>Expression of Interest</strong></p>
<ul>
<li dir="auto">Complete membership form</li>
<li dir="auto">Schedule pastoral meeting</li>
<li dir="auto">Provide baptism certificate (if applicable)</li>
</ul>
</li>
<li dir="auto">
<p><strong>Preparation</strong></p>
<ul>
<li dir="auto">Attend membership classes</li>
<li dir="auto">Meet with Church Council representative</li>
<li dir="auto">Complete Safe Church requirements</li>
</ul>
</li>
<li dir="auto">
<p><strong>Confirmation</strong></p>
<ul>
<li dir="auto">Church Council approval</li>
<li dir="auto">Welcome service planning</li>
<li dir="auto">Certificate preparation</li>
<li dir="auto">Update database status</li>
</ul>
</li>
<li dir="auto">
<p><strong>Post-Confirmation</strong></p>
<ul>
<li dir="auto">Add to member directory</li>
<li dir="auto">Issue name badge</li>
<li dir="auto">Connect with ministries</li>
<li dir="auto">Schedule follow-up</li>
</ul>
</li>
</ol>
<h3 data-heading="4.2 Member Directory Management" dir="auto">4.2 Member Directory Management</h3>
<h4 data-heading="Database Maintenance" dir="auto">Database Maintenance</h4>
<p dir="auto"><strong>Monthly Tasks:</strong></p>
<ol>
<li dir="auto">Review and update contact details</li>
<li dir="auto">Process change notifications</li>
<li dir="auto">Update family connections</li>
<li dir="auto">Archive inactive records</li>
</ol>
<p dir="auto"><strong>Quarterly Tasks:</strong></p>
<ol>
<li dir="auto">Generate directory proof</li>
<li dir="auto">Send for member verification</li>
<li dir="auto">Process corrections</li>
<li dir="auto">Publish updated directory</li>
</ol>
<h4 data-heading="Privacy Considerations" dir="auto">Privacy Considerations</h4>
<p dir="auto"><strong>Information Handling:</strong></p>
<ul>
<li dir="auto">Obtain written consent for directory inclusion</li>
<li dir="auto">Mark confidential information</li>
<li dir="auto">Limit access to authorized persons</li>
<li dir="auto">Secure physical and digital copies</li>
</ul>
<p dir="auto"><strong>Directory Distribution:</strong></p>
<ul>
<li dir="auto">Members only</li>
<li dir="auto">Mark "Confidential"</li>
<li dir="auto">Track distribution</li>
<li dir="auto">Retrieve old copies</li>
</ul>
<h3 data-heading="4.3 Name Badge Orders" dir="auto">4.3 Name Badge Orders</h3>
<h4 data-heading="Ordering Process" dir="auto">Ordering Process</h4>
<p dir="auto"><strong>Monthly Schedule:</strong></p>
<ol>
<li dir="auto">
<p><strong>Collection (1st-20th)</strong></p>
<ul>
<li dir="auto">Maintain order list</li>
<li dir="auto">Verify spelling</li>
<li dir="auto">Confirm badge type</li>
</ul>
</li>
<li dir="auto">
<p><strong>Processing (21st)</strong></p>
<ul>
<li dir="auto">Compile order</li>
<li dir="auto">Submit to supplier</li>
<li dir="auto">Process payment</li>
<li dir="auto">File order confirmation</li>
</ul>
</li>
<li dir="auto">
<p><strong>Distribution (Month end)</strong></p>
<ul>
<li dir="auto">Check order accuracy</li>
<li dir="auto">Notify recipients</li>
<li dir="auto">Arrange collection</li>
<li dir="auto">Update records</li>
</ul>
</li>
</ol>
<h4 data-heading="Badge Types" dir="auto">Badge Types</h4>
<ul>
<li dir="auto"><strong>Member Badge</strong>: Blue background</li>
<li dir="auto"><strong>Visitor Badge</strong>: Green background</li>
<li dir="auto"><strong>Staff Badge</strong>: Gold background</li>
<li dir="auto"><strong>Volunteer Badge</strong>: Red background</li>
</ul>
<h3 data-heading="4.4 Welcome Cards" dir="auto">4.4 Welcome Cards</h3>
<h4 data-heading="Visitor Welcome Cards" dir="auto">Visitor Welcome Cards</h4>
<p dir="auto"><strong>Processing Steps:</strong></p>
<ol>
<li dir="auto">
<p><strong>Collection</strong></p>
<ul>
<li dir="auto">Collect after each service</li>
<li dir="auto">Review for completeness</li>
<li dir="auto">Date stamp</li>
</ul>
</li>
<li dir="auto">
<p><strong>Data Entry</strong></p>
<ul>
<li dir="auto">Enter in visitor database</li>
<li dir="auto">Check for previous visits</li>
<li dir="auto">Note interests/requests</li>
</ul>
</li>
<li dir="auto">
<p><strong>Follow-up</strong></p>
<ul>
<li dir="auto">Send welcome email/letter (within 48 hours)</li>
<li dir="auto">Add to mailing list (if requested)</li>
<li dir="auto">Schedule pastoral contact (if requested)</li>
<li dir="auto">File card</li>
</ul>
</li>
</ol>
<h4 data-heading="Information Requested" dir="auto">Information Requested</h4>
<ul>
<li dir="auto">Name and contact details</li>
<li dir="auto">First time or returning visitor</li>
<li dir="auto">How they heard about church</li>
<li dir="auto">Areas of interest</li>
<li dir="auto">Prayer requests</li>
<li dir="auto">Permission to contact</li>
</ul>
<h3 data-heading="4.5 Baptism Procedures" dir="auto">4.5 Baptism Procedures</h3>
<h4 data-heading="Initial Enquiry" dir="auto">Initial Enquiry</h4>
<ol>
<li dir="auto">
<p><strong>Information Provision</strong></p>
<ul>
<li dir="auto">Explain baptism process</li>
<li dir="auto">Provide information booklet</li>
<li dir="auto">Check ministry team availability</li>
<li dir="auto">Schedule initial meeting</li>
</ul>
</li>
<li dir="auto">
<p><strong>Documentation</strong></p>
<ul>
<li dir="auto">Create baptism file</li>
<li dir="auto">Record enquiry details</li>
<li dir="auto">Note preferred dates</li>
<li dir="auto">Start checklist</li>
</ul>
</li>
</ol>
<h4 data-heading="Preparation Process" dir="auto">Preparation Process</h4>
<p dir="auto"><strong>6 Weeks Before:</strong></p>
<ul>
<li dir="auto">Confirm date and time</li>
<li dir="auto">Begin preparation classes</li>
<li dir="auto">Order certificate</li>
</ul>
<p dir="auto"><strong>4 Weeks Before:</strong></p>
<ul>
<li dir="auto">Confirm participants</li>
<li dir="auto">Arrange photography</li>
<li dir="auto">Plan service details</li>
</ul>
<p dir="auto"><strong>2 Weeks Before:</strong></p>
<ul>
<li dir="auto">Finalize service order</li>
<li dir="auto">Confirm catering (if applicable)</li>
<li dir="auto">Prepare certificates</li>
</ul>
<p dir="auto"><strong>1 Week Before:</strong></p>
<ul>
<li dir="auto">Final confirmation calls</li>
<li dir="auto">Rehearsal (if required)</li>
<li dir="auto">Set up requirements</li>
</ul>
<p dir="auto"><strong>Day of Baptism:</strong></p>
<ul>
<li dir="auto">Prepare venue</li>
<li dir="auto">Check all materials</li>
<li dir="auto">Coordinate participants</li>
<li dir="auto">Process certificates</li>
</ul>
<p dir="auto"><strong>After Baptism:</strong></p>
<ul>
<li dir="auto">File documentation</li>
<li dir="auto">Update member records</li>
<li dir="auto">Send photos</li>
<li dir="auto">Archive records</li>
</ul>
<h3 data-heading="4.6 Wedding Procedures" dir="auto">4.6 Wedding Procedures</h3>
<h4 data-heading="Initial Booking" dir="auto">Initial Booking</h4>
<p dir="auto"><strong>Requirements Check:</strong></p>
<ul>
<li dir="auto">One party must be church member</li>
<li dir="auto">Date availability</li>
<li dir="auto">Minister availability</li>
<li dir="auto">Legal requirements met</li>
</ul>
<p dir="auto"><strong>Documentation:</strong></p>
<ul>
<li dir="auto">Notice of Intended Marriage</li>
<li dir="auto">Birth certificates</li>
<li dir="auto">Divorce papers (if applicable)</li>
<li dir="auto">Death certificate (if applicable)</li>
</ul>
<h4 data-heading="Wedding Timeline" dir="auto">Wedding Timeline</h4>
<p dir="auto"><strong>3 Months Before:</strong></p>
<ul>
<li dir="auto">Confirm booking</li>
<li dir="auto">Process deposit</li>
<li dir="auto">Schedule preparation sessions</li>
<li dir="auto">Book rehearsal</li>
</ul>
<p dir="auto"><strong>1 Month Before:</strong></p>
<ul>
<li dir="auto">Finalize service details</li>
<li dir="auto">Confirm suppliers</li>
<li dir="auto">Review legal documents</li>
<li dir="auto">Prepare certificate</li>
</ul>
<p dir="auto"><strong>1 Week Before:</strong></p>
<ul>
<li dir="auto">Conduct rehearsal</li>
<li dir="auto">Final confirmations</li>
<li dir="auto">Prepare venue</li>
<li dir="auto">Brief volunteers</li>
</ul>
<p dir="auto"><strong>Wedding Day:</strong></p>
<ul>
<li dir="auto">Open and prepare venue</li>
<li dir="auto">Coordinate suppliers</li>
<li dir="auto">Support wedding party</li>
<li dir="auto">Process documentation</li>
</ul>
<p dir="auto"><strong>After Wedding:</strong></p>
<ul>
<li dir="auto">Submit legal documents</li>
<li dir="auto">Process final payment</li>
<li dir="auto">File church records</li>
<li dir="auto">Send certificate copy</li>
</ul>
<h3 data-heading="4.7 Funeral &amp; Memorial Services" dir="auto">4.7 Funeral &amp; Memorial Services</h3>
<h4 data-heading="Initial Contact" dir="auto">Initial Contact</h4>
<p dir="auto"><strong>Immediate Response:</strong></p>
<ol>
<li dir="auto">Express condolences</li>
<li dir="auto">Gather basic information</li>
<li dir="auto">Check venue availability</li>
<li dir="auto">Arrange pastoral visit</li>
</ol>
<p dir="auto"><strong>Information Required:</strong></p>
<ul>
<li dir="auto">Deceased details</li>
<li dir="auto">Family contacts</li>
<li dir="auto">Preferred date/time</li>
<li dir="auto">Funeral director</li>
<li dir="auto">Special requests</li>
</ul>
<h4 data-heading="Service Coordination" dir="auto">Service Coordination</h4>
<p dir="auto"><strong>Planning:</strong></p>
<ul>
<li dir="auto">Meet with family</li>
<li dir="auto">Plan service order</li>
<li dir="auto">Coordinate with funeral director</li>
<li dir="auto">Arrange musicians</li>
<li dir="auto">Prepare venue</li>
</ul>
<p dir="auto"><strong>Documentation:</strong></p>
<ul>
<li dir="auto">Service order</li>
<li dir="auto">Attendance register</li>
<li dir="auto">Memorial book</li>
<li dir="auto">Death register entry</li>
</ul>
<p dir="auto"><strong>Follow-up:</strong></p>
<ul>
<li dir="auto">Pastoral care contact</li>
<li dir="auto">Grief support information</li>
<li dir="auto">Memorial arrangements</li>
<li dir="auto">Database update</li>
</ul>
<h3 data-heading="4.8 Cemetery Enquiries" dir="auto">4.8 Cemetery Enquiries</h3>
<h4 data-heading="Information Requests" dir="auto">Information Requests</h4>
<p dir="auto"><strong>Common Enquiries:</strong></p>
<ul>
<li dir="auto">Burial locations</li>
<li dir="auto">Historical records</li>
<li dir="auto">Genealogy research</li>
<li dir="auto">Memorial permissions</li>
</ul>
<p dir="auto"><strong>Response Process:</strong></p>
<ol>
<li dir="auto">Check available records</li>
<li dir="auto">Refer to cemetery trust if needed</li>
<li dir="auto">Provide written response</li>
<li dir="auto">Maintain enquiry log</li>
</ol>
<h4 data-heading="Record Management" dir="auto">Record Management</h4>
<p dir="auto"><strong>Available Records:</strong></p>
<ul>
<li dir="auto">Historic burial register (pre-1990)</li>
<li dir="auto">Memorial inscriptions</li>
<li dir="auto">Plot maps</li>
<li dir="auto">Trust contact details</li>
</ul>
<p dir="auto"><strong>Limitations:</strong></p>
<ul>
<li dir="auto">No authority over cemetery operations</li>
<li dir="auto">Historical records only</li>
<li dir="auto">Refer current matters to trust</li>
</ul>
<hr>
<h2 data-heading="Part 5: Communications" dir="auto">Part 5: Communications</h2>
<h3 data-heading="5.1 Weekly Bulletin Preparation" dir="auto">5.1 Weekly Bulletin Preparation</h3>
<h4 data-heading="Production Schedule" dir="auto">Production Schedule</h4>
<p dir="auto"><strong>Monday:</strong></p>
<ul>
<li dir="auto">Review previous bulletin</li>
<li dir="auto">Check church calendar</li>
<li dir="auto">Gather announcements</li>
<li dir="auto">Contact contributors</li>
</ul>
<p dir="auto"><strong>Tuesday:</strong></p>
<ul>
<li dir="auto">Compile content</li>
<li dir="auto">Edit submissions</li>
<li dir="auto">Source images</li>
<li dir="auto">Draft layout</li>
</ul>
<p dir="auto"><strong>Wednesday:</strong></p>
<ul>
<li dir="auto">Finalize content</li>
<li dir="auto">Proofread</li>
<li dir="auto">Ministry team approval</li>
<li dir="auto">Prepare for printing</li>
</ul>
<p dir="auto"><strong>Thursday:</strong></p>
<ul>
<li dir="auto">Print bulletins</li>
<li dir="auto">Fold and collate</li>
<li dir="auto">Prepare for distribution</li>
<li dir="auto">Upload digital version</li>
</ul>
<p dir="auto"><strong>Friday:</strong></p>
<ul>
<li dir="auto">Distribute to preparers</li>
<li dir="auto">Email digital version</li>
<li dir="auto">Archive master copy</li>
</ul>
<h4 data-heading="Content Guidelines" dir="auto">Content Guidelines</h4>
<p dir="auto"><strong>Standard Sections:</strong></p>
<ol>
<li dir="auto">Welcome message</li>
<li dir="auto">Service details</li>
<li dir="auto">Prayer points</li>
<li dir="auto">Announcements</li>
<li dir="auto">Upcoming events</li>
<li dir="auto">Contact information</li>
</ol>
<p dir="auto"><strong>Style Requirements:</strong></p>
<ul>
<li dir="auto">Clear, readable fonts</li>
<li dir="auto">Consistent formatting</li>
<li dir="auto">High contrast printing</li>
<li dir="auto">Accessible language</li>
<li dir="auto">Correct spelling of names</li>
</ul>
<h3 data-heading="5.2 Bulletin Distribution" dir="auto">5.2 Bulletin Distribution</h3>
<h4 data-heading="Distribution Methods" dir="auto">Distribution Methods</h4>
<p dir="auto"><strong>Physical Copies:</strong></p>
<ul>
<li dir="auto">Service distribution: Front door and seats</li>
<li dir="auto">Office collection: Reception desk</li>
<li dir="auto">Mail: Homebound members</li>
<li dir="auto">Community boards: Local businesses</li>
</ul>
<p dir="auto"><strong>Digital Distribution:</strong></p>
<ul>
<li dir="auto">Email list: Thursday afternoon</li>
<li dir="auto">Website: Thursday evening</li>
<li dir="auto">Social media: Friday morning</li>
<li dir="auto">App notification: Friday morning</li>
</ul>
<h4 data-heading="Mailing List Management" dir="auto">Mailing List Management</h4>
<p dir="auto"><strong>Adding Recipients:</strong></p>
<ul>
<li dir="auto">Verify email address</li>
<li dir="auto">Add to appropriate list</li>
<li dir="auto">Send welcome message</li>
<li dir="auto">Note preferences</li>
</ul>
<p dir="auto"><strong>Removing Recipients:</strong></p>
<ul>
<li dir="auto">Process immediately</li>
<li dir="auto">Confirm removal</li>
<li dir="auto">Archive details</li>
<li dir="auto">Update records</li>
</ul>
<h3 data-heading="5.3 Website Management" dir="auto">5.3 Website Management</h3>
<h4 data-heading="Content Updates" dir="auto">Content Updates</h4>
<p dir="auto"><strong>Regular Updates:</strong></p>
<ul>
<li dir="auto">Service times</li>
<li dir="auto">Contact information</li>
<li dir="auto">Upcoming events</li>
<li dir="auto">News items</li>
<li dir="auto">Photos/galleries</li>
</ul>
<p dir="auto"><strong>Update Schedule:</strong></p>
<ul>
<li dir="auto">Daily: Urgent notices</li>
<li dir="auto">Weekly: Events and news</li>
<li dir="auto">Monthly: Calendar review</li>
<li dir="auto">Quarterly: Full content review</li>
</ul>
<h4 data-heading="Website Procedures" dir="auto">Website Procedures</h4>
<ol>
<li dir="auto">
<p><strong>Content Creation</strong></p>
<ul>
<li dir="auto">Write in plain language</li>
<li dir="auto">Include relevant images</li>
<li dir="auto">Check facts and dates</li>
<li dir="auto">Obtain approvals</li>
</ul>
</li>
<li dir="auto">
<p><strong>Publishing</strong></p>
<ul>
<li dir="auto">Log into admin panel</li>
<li dir="auto">Create/edit page</li>
<li dir="auto">Preview changes</li>
<li dir="auto">Publish when approved</li>
</ul>
</li>
<li dir="auto">
<p><strong>Maintenance</strong></p>
<ul>
<li dir="auto">Check broken links</li>
<li dir="auto">Update outdated content</li>
<li dir="auto">Archive old events</li>
<li dir="auto">Backup regularly</li>
</ul>
</li>
</ol>
<h3 data-heading="5.4 Social Media Guidelines" dir="auto">5.4 Social Media Guidelines</h3>
<h4 data-heading="Platform Management" dir="auto">Platform Management</h4>
<p dir="auto"><strong>Active Platforms:</strong></p>
<ul>
<li dir="auto">Facebook: Daily monitoring</li>
<li dir="auto">Instagram: 3-4 posts weekly</li>
<li dir="auto">YouTube: Service recordings</li>
<li dir="auto">Email newsletter: Weekly</li>
</ul>
<h4 data-heading="Content Guidelines" dir="auto">Content Guidelines</h4>
<p dir="auto"><strong>Appropriate Content:</strong></p>
<ul>
<li dir="auto">Service information</li>
<li dir="auto">Event promotion</li>
<li dir="auto">Inspirational messages</li>
<li dir="auto">Community news</li>
<li dir="auto">Photos (with permission)</li>
</ul>
<p dir="auto"><strong>Prohibited Content:</strong></p>
<ul>
<li dir="auto">Political statements</li>
<li dir="auto">Personal opinions</li>
<li dir="auto">Confidential information</li>
<li dir="auto">Unverified information</li>
<li dir="auto">Copyright material</li>
</ul>
<h4 data-heading="Engagement Protocols" dir="auto">Engagement Protocols</h4>
<p dir="auto"><strong>Responding to Comments:</strong></p>
<ul>
<li dir="auto">Respond within 24 hours</li>
<li dir="auto">Be respectful and kind</li>
<li dir="auto">Refer complex issues offline</li>
<li dir="auto">Delete inappropriate content</li>
<li dir="auto">Document concerning interactions</li>
</ul>
<h3 data-heading="5.5 Mass Email Communications" dir="auto">5.5 Mass Email Communications</h3>
<h4 data-heading="Email Categories" dir="auto">Email Categories</h4>
<ol>
<li dir="auto">
<p><strong>Weekly Newsletter</strong></p>
<ul>
<li dir="auto">Sent Thursday afternoons</li>
<li dir="auto">Bulletin content plus extras</li>
<li dir="auto">Events and announcements</li>
</ul>
</li>
<li dir="auto">
<p><strong>Urgent Notices</strong></p>
<ul>
<li dir="auto">Service cancellations</li>
<li dir="auto">Emergency information</li>
<li dir="auto">Time-sensitive updates</li>
</ul>
</li>
<li dir="auto">
<p><strong>Special Events</strong></p>
<ul>
<li dir="auto">Event invitations</li>
<li dir="auto">Registration reminders</li>
<li dir="auto">Follow-up information</li>
</ul>
</li>
</ol>
<h4 data-heading="Best Practices" dir="auto">Best Practices</h4>
<p dir="auto"><strong>Email Creation:</strong></p>
<ul>
<li dir="auto">Clear subject line</li>
<li dir="auto">Mobile-friendly format</li>
<li dir="auto">Include unsubscribe option</li>
<li dir="auto">Test before sending</li>
<li dir="auto">Archive sent emails</li>
</ul>
<p dir="auto"><strong>Privacy Protection:</strong></p>
<ul>
<li dir="auto">Use BCC for group emails</li>
<li dir="auto">Don't share email lists</li>
<li dir="auto">Respect unsubscribe requests</li>
<li dir="auto">Secure email databases</li>
</ul>
<h3 data-heading="5.6 Notice Board Management" dir="auto">5.6 Notice Board Management</h3>
<h4 data-heading="Physical Notice Boards" dir="auto">Physical Notice Boards</h4>
<p dir="auto"><strong>Location Management:</strong></p>
<ul>
<li dir="auto">Main entrance: Current events</li>
<li dir="auto">Hall: Community notices</li>
<li dir="auto">Office: Rosters and internal</li>
<li dir="auto">Foyer: General information</li>
</ul>
<p dir="auto"><strong>Maintenance Schedule:</strong></p>
<ul>
<li dir="auto">Daily: Remove expired notices</li>
<li dir="auto">Weekly: Organize and tidy</li>
<li dir="auto">Monthly: Deep clean</li>
<li dir="auto">Quarterly: Review policy</li>
</ul>
<h4 data-heading="Notice Approval" dir="auto">Notice Approval</h4>
<p dir="auto"><strong>Approval Required For:</strong></p>
<ul>
<li dir="auto">External organizations</li>
<li dir="auto">Fundraising activities</li>
<li dir="auto">Commercial services</li>
<li dir="auto">Political/controversial topics</li>
</ul>
<p dir="auto"><strong>Approval Process:</strong></p>
<ol>
<li dir="auto">Review content</li>
<li dir="auto">Check alignment with values</li>
<li dir="auto">Determine display period</li>
<li dir="auto">Mark with removal date</li>
<li dir="auto">File copy</li>
</ol>
<hr>
<h2 data-heading="Part 6: Property &amp; Facilities" dir="auto">Part 6: Property &amp; Facilities</h2>
<h3 data-heading="6.1 Hall Hire Procedures" dir="auto">6.1 Hall Hire Procedures</h3>
<h4 data-heading="Enquiry Process" dir="auto">Enquiry Process</h4>
<ol>
<li dir="auto">
<p><strong>Initial Contact</strong></p>
<ul>
<li dir="auto">Check availability</li>
<li dir="auto">Explain facilities</li>
<li dir="auto">Provide rate card</li>
<li dir="auto">Offer viewing</li>
</ul>
</li>
<li dir="auto">
<p><strong>Viewing Arrangement</strong></p>
<ul>
<li dir="auto">Schedule convenient time</li>
<li dir="auto">Show all facilities</li>
<li dir="auto">Explain conditions</li>
<li dir="auto">Answer questions</li>
</ul>
</li>
<li dir="auto">
<p><strong>Quote Preparation</strong></p>
<ul>
<li dir="auto">Calculate hire fee</li>
<li dir="auto">Add equipment costs</li>
<li dir="auto">Include security deposit</li>
<li dir="auto">Provide terms</li>
</ul>
</li>
</ol>
<h4 data-heading="Booking Process" dir="auto">Booking Process</h4>
<p dir="auto"><strong>Documentation Required:</strong></p>
<ul>
<li dir="auto">Completed application form</li>
<li dir="auto">Signed terms and conditions</li>
<li dir="auto">Insurance certificate</li>
<li dir="auto">Payment of deposit</li>
</ul>
<p dir="auto"><strong>Booking Confirmation:</strong></p>
<ol>
<li dir="auto">Process application</li>
<li dir="auto">Check references (if new)</li>
<li dir="auto">Confirm insurance</li>
<li dir="auto">Issue confirmation letter</li>
<li dir="auto">Update calendar</li>
<li dir="auto">Create hire file</li>
</ol>
<h4 data-heading="Hire Categories" dir="auto">Hire Categories</h4>
<p dir="auto"><strong>Regular Hirers:</strong></p>
<ul>
<li dir="auto">Fixed weekly/monthly bookings</li>
<li dir="auto">Invoiced monthly</li>
<li dir="auto">Annual agreement review</li>
<li dir="auto">Key holder status</li>
</ul>
<p dir="auto"><strong>Casual Hirers:</strong></p>
<ul>
<li dir="auto">One-off bookings</li>
<li dir="auto">Payment in advance</li>
<li dir="auto">Full orientation required</li>
<li dir="auto">Keys returned immediately</li>
</ul>
<p dir="auto"><strong>Community Groups:</strong></p>
<ul>
<li dir="auto">Discounted rates may apply</li>
<li dir="auto">Church Council approval</li>
<li dir="auto">Community benefit demonstrated</li>
</ul>
<h3 data-heading="6.2 Church Property Bookings" dir="auto">6.2 Church Property Bookings</h3>
<h4 data-heading="Internal Bookings" dir="auto">Internal Bookings</h4>
<p dir="auto"><strong>Booking Authority:</strong></p>
<ul>
<li dir="auto">Worship space: Ministry team</li>
<li dir="auto">Meeting rooms: Administrator</li>
<li dir="auto">Kitchen: Administrator</li>
<li dir="auto">Equipment: Property officer</li>
</ul>
<p dir="auto"><strong>Booking Process:</strong></p>
<ol>
<li dir="auto">Check calendar</li>
<li dir="auto">Confirm requirements</li>
<li dir="auto">Note in diary</li>
<li dir="auto">Update online calendar</li>
<li dir="auto">Notify relevant parties</li>
</ol>
<h4 data-heading="Booking Priorities" dir="auto">Booking Priorities</h4>
<p dir="auto"><strong>Priority Order:</strong></p>
<ol>
<li dir="auto">Worship services</li>
<li dir="auto">Church programs</li>
<li dir="auto">Member events</li>
<li dir="auto">Community groups</li>
<li dir="auto">External hire</li>
</ol>
<p dir="auto"><strong>Conflicts Resolution:</strong></p>
<ul>
<li dir="auto">First booked basis</li>
<li dir="auto">Ministry team arbitration</li>
<li dir="auto">Alternative options offered</li>
<li dir="auto">Clear communication</li>
</ul>
<h3 data-heading="6.3 Keys &amp; Security" dir="auto">6.3 Keys &amp; Security</h3>
<h4 data-heading="Key Register" dir="auto">Key Register</h4>
<p dir="auto"><strong>Information Recorded:</strong></p>
<ul>
<li dir="auto">Key number</li>
<li dir="auto">Area access</li>
<li dir="auto">Holder name</li>
<li dir="auto">Issue date</li>
<li dir="auto">Return date</li>
<li dir="auto">Agreement signed</li>
</ul>
<h4 data-heading="Key Management" dir="auto">Key Management</h4>
<p dir="auto"><strong>Issue Process:</strong></p>
<ol>
<li dir="auto">Verify authorization</li>
<li dir="auto">Complete key agreement</li>
<li dir="auto">Record in register</li>
<li dir="auto">Provide key</li>
<li dir="auto">Explain responsibilities</li>
</ol>
<p dir="auto"><strong>Return Process:</strong></p>
<ol>
<li dir="auto">Receive key</li>
<li dir="auto">Check condition</li>
<li dir="auto">Update register</li>
<li dir="auto">File agreement</li>
<li dir="auto">Confirm access removed</li>
</ol>
<h4 data-heading="Security Protocols" dir="auto">Security Protocols</h4>
<p dir="auto"><strong>Daily Security:</strong></p>
<ul>
<li dir="auto">Alarm codes confidential</li>
<li dir="auto">Regular code changes</li>
<li dir="auto">Incident reporting</li>
<li dir="auto">Emergency procedures</li>
</ul>
<p dir="auto"><strong>Access Control:</strong></p>
<ul>
<li dir="auto">Limited key distribution</li>
<li dir="auto">Regular audit</li>
<li dir="auto">Lost key procedure</li>
<li dir="auto">Lock changes when required</li>
</ul>
<h3 data-heading="6.4 Equipment Maintenance" dir="auto">6.4 Equipment Maintenance</h3>
<h4 data-heading="Maintenance Schedule" dir="auto">Maintenance Schedule</h4>
<p dir="auto"><strong>Daily:</strong></p>
<ul>
<li dir="auto">Visual inspections</li>
<li dir="auto">Basic cleaning</li>
<li dir="auto">Function testing</li>
</ul>
<p dir="auto"><strong>Weekly:</strong></p>
<ul>
<li dir="auto">Detailed cleaning</li>
<li dir="auto">Supply checks</li>
<li dir="auto">Minor repairs</li>
</ul>
<p dir="auto"><strong>Monthly:</strong></p>
<ul>
<li dir="auto">Deep cleaning</li>
<li dir="auto">Preventive maintenance</li>
<li dir="auto">Inventory check</li>
</ul>
<p dir="auto"><strong>Annual:</strong></p>
<ul>
<li dir="auto">Professional servicing</li>
<li dir="auto">Safety inspections</li>
<li dir="auto">Equipment audit</li>
<li dir="auto">Replacement planning</li>
</ul>
<h4 data-heading="Equipment Categories" dir="auto">Equipment Categories</h4>
<p dir="auto"><strong>Kitchen Equipment:</strong></p>
<ul>
<li dir="auto">Ovens and cooktops</li>
<li dir="auto">Refrigerators</li>
<li dir="auto">Dishwashers</li>
<li dir="auto">Small appliances</li>
</ul>
<p dir="auto"><strong>Office Equipment:</strong></p>
<ul>
<li dir="auto">Computers</li>
<li dir="auto">Printers/copiers</li>
<li dir="auto">Phones</li>
<li dir="auto">Audio-visual</li>
</ul>
<p dir="auto"><strong>Facility Equipment:</strong></p>
<ul>
<li dir="auto">Air conditioning</li>
<li dir="auto">Sound system</li>
<li dir="auto">Lighting</li>
<li dir="auto">Security systems</li>
</ul>
<h3 data-heading="6.5 Cleaning Schedules" dir="auto">6.5 Cleaning Schedules</h3>
<h4 data-heading="Regular Cleaning" dir="auto">Regular Cleaning</h4>
<p dir="auto"><strong>Daily Tasks:</strong></p>
<ul>
<li dir="auto">Empty bins</li>
<li dir="auto">Clean bathrooms</li>
<li dir="auto">Vacuum high-traffic areas</li>
<li dir="auto">Wipe surfaces</li>
<li dir="auto">Lock and secure</li>
</ul>
<p dir="auto"><strong>Weekly Tasks:</strong></p>
<ul>
<li dir="auto">Mop floors</li>
<li dir="auto">Clean kitchen thoroughly</li>
<li dir="auto">Dust all areas</li>
<li dir="auto">Clean windows</li>
<li dir="auto">Restock supplies</li>
</ul>
<p dir="auto"><strong>Monthly Tasks:</strong></p>
<ul>
<li dir="auto">Deep clean bathrooms</li>
<li dir="auto">Clean light fittings</li>
<li dir="auto">Wash walls (as needed)</li>
<li dir="auto">Equipment deep clean</li>
<li dir="auto">Outdoor areas</li>
</ul>
<h4 data-heading="Cleaning Coordination" dir="auto">Cleaning Coordination</h4>
<p dir="auto"><strong>Volunteer Rosters:</strong></p>
<ul>
<li dir="auto">Weekly allocation</li>
<li dir="auto">Clear task lists</li>
<li dir="auto">Supply provision</li>
<li dir="auto">Training provided</li>
<li dir="auto">Recognition system</li>
</ul>
<p dir="auto"><strong>Professional Cleaning:</strong></p>
<ul>
<li dir="auto">Quarterly deep clean</li>
<li dir="auto">Annual carpet cleaning</li>
<li dir="auto">Window cleaning</li>
<li dir="auto">Specialist tasks</li>
</ul>
<hr>
<h2 data-heading="Part 7: Volunteer Management" dir="auto">Part 7: Volunteer Management</h2>
<h3 data-heading="7.1 Volunteer Registration" dir="auto">7.1 Volunteer Registration</h3>
<h4 data-heading="Application Process" dir="auto">Application Process</h4>
<ol>
<li dir="auto">
<p><strong>Initial Interest</strong></p>
<ul>
<li dir="auto">Complete volunteer form</li>
<li dir="auto">Indicate areas of interest</li>
<li dir="auto">Provide references</li>
<li dir="auto">Submit Blue Card (if required)</li>
</ul>
</li>
<li dir="auto">
<p><strong>Screening</strong></p>
<ul>
<li dir="auto">Reference checks</li>
<li dir="auto">Blue Card verification</li>
<li dir="auto">Interview (if required)</li>
<li dir="auto">Skills assessment</li>
</ul>
</li>
<li dir="auto">
<p><strong>Placement</strong></p>
<ul>
<li dir="auto">Match skills to needs</li>
<li dir="auto">Confirm availability</li>
<li dir="auto">Assign mentor</li>
<li dir="auto">Schedule training</li>
</ul>
</li>
</ol>
<h4 data-heading="Documentation" dir="auto">Documentation</h4>
<p dir="auto"><strong>Required Records:</strong></p>
<ul>
<li dir="auto">Application form</li>
<li dir="auto">Reference checks</li>
<li dir="auto">Blue Card details</li>
<li dir="auto">Emergency contacts</li>
<li dir="auto">Training records</li>
<li dir="auto">Agreement signed</li>
</ul>
<h3 data-heading="7.2 Volunteer Rights &amp; Responsibilities" dir="auto">7.2 Volunteer Rights &amp; Responsibilities</h3>
<h4 data-heading="Volunteer Rights" dir="auto">Volunteer Rights</h4>
<p dir="auto"><strong>Every volunteer has the right to:</strong></p>
<ul>
<li dir="auto">Safe working environment</li>
<li dir="auto">Clear role description</li>
<li dir="auto">Adequate training</li>
<li dir="auto">Appropriate supervision</li>
<li dir="auto">Recognition and respect</li>
<li dir="auto">Insurance coverage</li>
<li dir="auto">Reimbursement of approved expenses</li>
<li dir="auto">Decline tasks outside role</li>
<li dir="auto">Provide feedback</li>
<li dir="auto">Withdraw from role</li>
</ul>
<h4 data-heading="Volunteer Responsibilities" dir="auto">Volunteer Responsibilities</h4>
<p dir="auto"><strong>Every volunteer must:</strong></p>
<ul>
<li dir="auto">Fulfill commitment reliably</li>
<li dir="auto">Maintain confidentiality</li>
<li dir="auto">Follow safety procedures</li>
<li dir="auto">Respect others</li>
<li dir="auto">Report incidents</li>
<li dir="auto">Participate in training</li>
<li dir="auto">Use resources responsibly</li>
<li dir="auto">Represent church positively</li>
<li dir="auto">Communicate availability</li>
<li dir="auto">Return church property</li>
</ul>
<h3 data-heading="7.3 Roster Management" dir="auto">7.3 Roster Management</h3>
<h4 data-heading="Roster Creation" dir="auto">Roster Creation</h4>
<p dir="auto"><strong>Monthly Process:</strong></p>
<ol>
<li dir="auto">
<p><strong>Planning (20th)</strong></p>
<ul>
<li dir="auto">Review upcoming needs</li>
<li dir="auto">Check special events</li>
<li dir="auto">Note absent volunteers</li>
</ul>
</li>
<li dir="auto">
<p><strong>Drafting (22nd)</strong></p>
<ul>
<li dir="auto">Allocate positions</li>
<li dir="auto">Balance workload</li>
<li dir="auto">Check availability</li>
</ul>
</li>
<li dir="auto">
<p><strong>Confirmation (25th)</strong></p>
<ul>
<li dir="auto">Contact volunteers</li>
<li dir="auto">Confirm availability</li>
<li dir="auto">Make adjustments</li>
</ul>
</li>
<li dir="auto">
<p><strong>Publication (28th)</strong></p>
<ul>
<li dir="auto">Finalize roster</li>
<li dir="auto">Distribute copies</li>
<li dir="auto">Post on notice board</li>
<li dir="auto">Email reminders</li>
</ul>
</li>
</ol>
<h4 data-heading="Roster Areas" dir="auto">Roster Areas</h4>
<p dir="auto"><strong>Worship Services:</strong></p>
<ul>
<li dir="auto">Welcome team</li>
<li dir="auto">Scripture readers</li>
<li dir="auto">Prayer leaders</li>
<li dir="auto">Musicians</li>
<li dir="auto">Technical team</li>
<li dir="auto">Morning tea</li>
</ul>
<p dir="auto"><strong>Office Support:</strong></p>
<ul>
<li dir="auto">Reception</li>
<li dir="auto">Bulletin preparation</li>
<li dir="auto">Mail processing</li>
<li dir="auto">Data entry</li>
<li dir="auto">Filing</li>
</ul>
<p dir="auto"><strong>Property:</strong></p>
<ul>
<li dir="auto">Cleaning</li>
<li dir="auto">Gardening</li>
<li dir="auto">Maintenance</li>
<li dir="auto">Security</li>
</ul>
<h3 data-heading="7.4 Training &amp; Induction" dir="auto">7.4 Training &amp; Induction</h3>
<h4 data-heading="Induction Program" dir="auto">Induction Program</h4>
<p dir="auto"><strong>Session 1: Orientation</strong></p>
<ul>
<li dir="auto">Church history and values</li>
<li dir="auto">Organizational structure</li>
<li dir="auto">Policies and procedures</li>
<li dir="auto">Safety briefing</li>
<li dir="auto">Tour of facilities</li>
</ul>
<p dir="auto"><strong>Session 2: Role-Specific</strong></p>
<ul>
<li dir="auto">Detailed role description</li>
<li dir="auto">Task training</li>
<li dir="auto">Systems and processes</li>
<li dir="auto">Shadow experienced volunteer</li>
<li dir="auto">Questions and practice</li>
</ul>
<h4 data-heading="Ongoing Training" dir="auto">Ongoing Training</h4>
<p dir="auto"><strong>Annual Requirements:</strong></p>
<ul>
<li dir="auto">Safe Church update</li>
<li dir="auto">Fire evacuation drill</li>
<li dir="auto">First aid awareness</li>
<li dir="auto">Role-specific updates</li>
</ul>
<p dir="auto"><strong>Optional Development:</strong></p>
<ul>
<li dir="auto">Leadership training</li>
<li dir="auto">Skills workshops</li>
<li dir="auto">Conference attendance</li>
<li dir="auto">Online courses</li>
</ul>
<hr>
<h2 data-heading="Part 8: Safe Church Compliance" dir="auto">Part 8: Safe Church Compliance</h2>
<h3 data-heading="8.1 Blue Card Requirements" dir="auto">8.1 Blue Card Requirements</h3>
<h4 data-heading="Who Needs a Blue Card" dir="auto">Who Needs a Blue Card</h4>
<p dir="auto"><strong>Mandatory Positions:</strong></p>
<ul>
<li dir="auto">All children's ministry volunteers</li>
<li dir="auto">Youth leaders and helpers</li>
<li dir="auto">Sunday School teachers</li>
<li dir="auto">Playgroup facilitators</li>
<li dir="auto">Anyone in regulated activities</li>
</ul>
<p dir="auto"><strong>Exemptions:</strong></p>
<ul>
<li dir="auto">Volunteers under 18</li>
<li dir="auto">Parents volunteering with own children</li>
<li dir="auto">One-off volunteers (conditions apply)</li>
</ul>
<h4 data-heading="Application Process" dir="auto">Application Process</h4>
<ol>
<li dir="auto">
<p><strong>New Applications</strong></p>
<ul>
<li dir="auto">Complete online application</li>
<li dir="auto">Provide identification</li>
<li dir="auto">Link to church organization</li>
<li dir="auto">Pay fee (if applicable)</li>
<li dir="auto">Await approval</li>
</ul>
</li>
<li dir="auto">
<p><strong>Renewals</strong></p>
<ul>
<li dir="auto">Apply before expiry</li>
<li dir="auto">Update details</li>
<li dir="auto">Confirm organization link</li>
<li dir="auto">Process payment</li>
</ul>
</li>
<li dir="auto">
<p><strong>Transfers</strong></p>
<ul>
<li dir="auto">Link existing card</li>
<li dir="auto">Notify previous organization</li>
<li dir="auto">Update church records</li>
</ul>
</li>
</ol>
<h3 data-heading="8.2 Blue Card Reporting" dir="auto">8.2 Blue Card Reporting</h3>
<h4 data-heading="Monthly Reporting" dir="auto">Monthly Reporting</h4>
<p dir="auto"><strong>Required Actions:</strong></p>
<ol>
<li dir="auto">Check expiry dates</li>
<li dir="auto">Send renewal reminders</li>
<li dir="auto">Update database</li>
<li dir="auto">Report changes to authorities</li>
</ol>
<p dir="auto"><strong>Change Notifications:</strong></p>
<ul>
<li dir="auto">New volunteers</li>
<li dir="auto">Ceased volunteers</li>
<li dir="auto">Contact detail changes</li>
<li dir="auto">Role changes</li>
</ul>
<h4 data-heading="Compliance Monitoring" dir="auto">Compliance Monitoring</h4>
<p dir="auto"><strong>Database Management:</strong></p>
<ul>
<li dir="auto">Current card details</li>
<li dir="auto">Expiry tracking</li>
<li dir="auto">Role assignments</li>
<li dir="auto">Training records</li>
</ul>
<p dir="auto"><strong>Reporting Requirements:</strong></p>
<ul>
<li dir="auto">Annual compliance audit</li>
<li dir="auto">Incident reports</li>
<li dir="auto">Change notifications</li>
<li dir="auto">Authority queries</li>
</ul>
<h3 data-heading="8.3 Safe Church Training" dir="auto">8.3 Safe Church Training</h3>
<h4 data-heading="Training Requirements" dir="auto">Training Requirements</h4>
<p dir="auto"><strong>Initial Training:</strong></p>
<ul>
<li dir="auto">Complete before commencing role</li>
<li dir="auto">Online or face-to-face options</li>
<li dir="auto">Certificate of completion</li>
<li dir="auto">Valid for 3 years</li>
</ul>
<p dir="auto"><strong>Refresher Training:</strong></p>
<ul>
<li dir="auto">Every 3 years</li>
<li dir="auto">Update on policy changes</li>
<li dir="auto">Scenario discussions</li>
<li dir="auto">Assessment completion</li>
</ul>
<h4 data-heading="Training Records" dir="auto">Training Records</h4>
<p dir="auto"><strong>Documentation:</strong></p>
<ul>
<li dir="auto">Training dates</li>
<li dir="auto">Certificate numbers</li>
<li dir="auto">Expiry dates</li>
<li dir="auto">Attendance records</li>
</ul>
<p dir="auto"><strong>Follow-up:</strong></p>
<ul>
<li dir="auto">3-month reminders</li>
<li dir="auto">Booking assistance</li>
<li dir="auto">Progress tracking</li>
<li dir="auto">Compliance reporting</li>
</ul>
<h3 data-heading="8.4 Incident Reporting" dir="auto">8.4 Incident Reporting</h3>
<h4 data-heading="Incident Types" dir="auto">Incident Types</h4>
<p dir="auto"><strong>Must Report:</strong></p>
<ul>
<li dir="auto">Injuries requiring treatment</li>
<li dir="auto">Child safety concerns</li>
<li dir="auto">Property damage</li>
<li dir="auto">Security breaches</li>
<li dir="auto">Policy violations</li>
<li dir="auto">Near misses</li>
</ul>
<h4 data-heading="Reporting Process" dir="auto">Reporting Process</h4>
<p dir="auto"><strong>Immediate Actions:</strong></p>
<ol>
<li dir="auto">Ensure safety</li>
<li dir="auto">Provide first aid</li>
<li dir="auto">Secure area</li>
<li dir="auto">Contact emergency services (if required)</li>
</ol>
<p dir="auto"><strong>Documentation:</strong></p>
<ol>
<li dir="auto">Complete incident form</li>
<li dir="auto">Gather witness statements</li>
<li dir="auto">Take photographs</li>
<li dir="auto">Secure evidence</li>
</ol>
<p dir="auto"><strong>Notification:</strong></p>
<ol>
<li dir="auto">Inform Ministry Team Leader</li>
<li dir="auto">Contact insurers (if required)</li>
<li dir="auto">Notify authorities (if required)</li>
<li dir="auto">Brief Church Council</li>
</ol>
<p dir="auto"><strong>Follow-up:</strong></p>
<ol>
<li dir="auto">Investigation</li>
<li dir="auto">Corrective actions</li>
<li dir="auto">Policy review</li>
<li dir="auto">Training needs</li>
</ol>
<h3 data-heading="8.5 Privacy &amp; Confidentiality" dir="auto">8.5 Privacy &amp; Confidentiality</h3>
<h4 data-heading="Information Classification" dir="auto">Information Classification</h4>
<p dir="auto"><strong>Public Information:</strong></p>
<ul>
<li dir="auto">Service times</li>
<li dir="auto">Contact details</li>
<li dir="auto">General programs</li>
<li dir="auto">Public events</li>
</ul>
<p dir="auto"><strong>Restricted Information:</strong></p>
<ul>
<li dir="auto">Member directory</li>
<li dir="auto">Financial records</li>
<li dir="auto">Meeting minutes</li>
<li dir="auto">Internal documents</li>
</ul>
<p dir="auto"><strong>Confidential Information:</strong></p>
<ul>
<li dir="auto">Pastoral matters</li>
<li dir="auto">Personal details</li>
<li dir="auto">Health information</li>
<li dir="auto">Legal matters</li>
</ul>
<h4 data-heading="Privacy Protocols" dir="auto">Privacy Protocols</h4>
<p dir="auto"><strong>Collection:</strong></p>
<ul>
<li dir="auto">Only collect necessary information</li>
<li dir="auto">Explain purpose</li>
<li dir="auto">Obtain consent</li>
<li dir="auto">Secure storage</li>
</ul>
<p dir="auto"><strong>Use:</strong></p>
<ul>
<li dir="auto">Only for stated purpose</li>
<li dir="auto">Authorized access only</li>
<li dir="auto">No unauthorized sharing</li>
<li dir="auto">Audit trail maintained</li>
</ul>
<p dir="auto"><strong>Disposal:</strong></p>
<ul>
<li dir="auto">Secure destruction</li>
<li dir="auto">Follow retention schedule</li>
<li dir="auto">Certificate of destruction</li>
<li dir="auto">Update records</li>
</ul>
<hr>
<h2 data-heading="Part 9: Administration Systems" dir="auto">Part 9: Administration Systems</h2>
<h3 data-heading="9.1 Database Management (Pastoral Care)" dir="auto">9.1 Database Management (Pastoral Care)</h3>
<h4 data-heading="System Overview" dir="auto">System Overview</h4>
<p dir="auto"><strong>Database Functions:</strong></p>
<ul>
<li dir="auto">Member records</li>
<li dir="auto">Contact management</li>
<li dir="auto">Donation tracking</li>
<li dir="auto">Event registration</li>
<li dir="auto">Communication lists</li>
<li dir="auto">Report generation</li>
</ul>
<h4 data-heading="Daily Operations" dir="auto">Daily Operations</h4>
<p dir="auto"><strong>Data Entry:</strong></p>
<ol>
<li dir="auto">Verify information accuracy</li>
<li dir="auto">Check for duplicates</li>
<li dir="auto">Enter complete details</li>
<li dir="auto">Link relationships</li>
<li dir="auto">Assign groups/categories</li>
</ol>
<p dir="auto"><strong>Data Maintenance:</strong></p>
<ul>
<li dir="auto">Regular backups</li>
<li dir="auto">Update verification</li>
<li dir="auto">Duplicate removal</li>
<li dir="auto">Archive inactive</li>
<li dir="auto">System optimization</li>
</ul>
<h4 data-heading="Report Generation" dir="auto">Report Generation</h4>
<p dir="auto"><strong>Standard Reports:</strong></p>
<ul>
<li dir="auto">Member directory</li>
<li dir="auto">Birthday lists</li>
<li dir="auto">Anniversary lists</li>
<li dir="auto">Donation summaries</li>
<li dir="auto">Attendance statistics</li>
<li dir="auto">Group lists</li>
</ul>
<p dir="auto"><strong>Custom Reports:</strong></p>
<ol>
<li dir="auto">Define requirements</li>
<li dir="auto">Set parameters</li>
<li dir="auto">Generate report</li>
<li dir="auto">Review accuracy</li>
<li dir="auto">Export format</li>
<li dir="auto">Distribute appropriately</li>
</ol>
<h3 data-heading="9.2 Document Filing System" dir="auto">9.2 Document Filing System</h3>
<h4 data-heading="Physical Filing" dir="auto">Physical Filing</h4>
<p dir="auto"><strong>Filing Structure:</strong></p>
<pre><code>A - Administration
  A1 - Policies
  A2 - Procedures
  A3 - Council Minutes
  A4 - Correspondence

F - Finance
  F1 - Budgets
  F2 - Reports
  F3 - Invoices
  F4 - Banking

M - Members
  M1 - Applications
  M2 - Directories
  M3 - Pastoral

P - Property
  P1 - Maintenance
  P2 - Hire Agreements
  P3 - Insurance
  P4 - Plans

S - Safe Church
  S1 - Blue Cards
  S2 - Training
  S3 - Incidents
  S4 - Policies
</code><button class="copy-code-button"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svg-icon lucide-copy"><rect x="8" y="8" width="14" height="14" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path><style>
:root {
  --default-font: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Microsoft YaHei Light", sans-serif;
  --font-monospace: 'Source Code Pro', monospace;
  --background-primary: #ffffff;
  --background-modifier-border: #ddd;
  --text-accent: #705dcf;
  --text-accent-hover: #7a6ae6;
  --text-normal: #2e3338;
  --background-secondary: #f2f3f5;
  --background-secondary-alt: #e3e5e8;
  --text-muted: #888888;
}</style>"<marker id="mermaid_arrowhead" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowheadPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker>"</svg></button></pre>
<h4 data-heading="Digital Filing" dir="auto">Digital Filing</h4>
<p dir="auto"><strong>Folder Structure:</strong></p>
<ul>
<li dir="auto">Mirror physical system</li>
<li dir="auto">Clear naming conventions</li>
<li dir="auto">Version control</li>
<li dir="auto">Regular archiving</li>
<li dir="auto">Cloud backup</li>
</ul>
<p dir="auto"><strong>File Naming:</strong><br>
Format: <code>YYYY-MM-DD_Category_Description</code><br>
Example: <code>2024-03-15_Finance_Monthly-Report</code></p>
<h3 data-heading="9.3 Annual Reports" dir="auto">9.3 Annual Reports</h3>
<h4 data-heading="Report Preparation Timeline" dir="auto">Report Preparation Timeline</h4>
<p dir="auto"><strong>October:</strong></p>
<ul>
<li dir="auto">Set report parameters</li>
<li dir="auto">Request submissions</li>
<li dir="auto">Create templates</li>
</ul>
<p dir="auto"><strong>November:</strong></p>
<ul>
<li dir="auto">Collect reports</li>
<li dir="auto">Follow up missing</li>
<li dir="auto">Begin compilation</li>
</ul>
<p dir="auto"><strong>December:</strong></p>
<ul>
<li dir="auto">Edit and format</li>
<li dir="auto">Review accuracy</li>
<li dir="auto">Obtain approvals</li>
</ul>
<p dir="auto"><strong>January:</strong></p>
<ul>
<li dir="auto">Final production</li>
<li dir="auto">Print copies</li>
<li dir="auto">Distribute</li>
</ul>
<h4 data-heading="Report Components" dir="auto">Report Components</h4>
<p dir="auto"><strong>Standard Sections:</strong></p>
<ol>
<li dir="auto">Ministry team report</li>
<li dir="auto">Church Council report</li>
<li dir="auto">Financial statements</li>
<li dir="auto">Ministry reports</li>
<li dir="auto">Property report</li>
<li dir="auto">Statistics</li>
<li dir="auto">Acknowledgments</li>
</ol>
<h3 data-heading="9.4 Monthly Reporting" dir="auto">9.4 Monthly Reporting</h3>
<h4 data-heading="Report Schedule" dir="auto">Report Schedule</h4>
<p dir="auto"><strong>First Week:</strong></p>
<ul>
<li dir="auto">Financial summary</li>
<li dir="auto">Attendance statistics</li>
<li dir="auto">New members</li>
<li dir="auto">Pastoral activities</li>
</ul>
<p dir="auto"><strong>Second Week:</strong></p>
<ul>
<li dir="auto">Property issues</li>
<li dir="auto">Volunteer hours</li>
<li dir="auto">Event summaries</li>
<li dir="auto">Correspondence log</li>
</ul>
<p dir="auto"><strong>Third Week:</strong></p>
<ul>
<li dir="auto">Compliance update</li>
<li dir="auto">Risk register</li>
<li dir="auto">Action items</li>
<li dir="auto">Forward planning</li>
</ul>
<p dir="auto"><strong>Fourth Week:</strong></p>
<ul>
<li dir="auto">Consolidated report</li>
<li dir="auto">Council preparation</li>
<li dir="auto">Distribution</li>
<li dir="auto">Filing</li>
</ul>
<h4 data-heading="Key Performance Indicators" dir="auto">Key Performance Indicators</h4>
<p dir="auto"><strong>Tracked Metrics:</strong></p>
<ul>
<li dir="auto">Service attendance</li>
<li dir="auto">Financial position</li>
<li dir="auto">Volunteer participation</li>
<li dir="auto">Facility utilization</li>
<li dir="auto">Member engagement</li>
<li dir="auto">Community impact</li>
</ul>
<h3 data-heading="9.5 Archiving Procedures" dir="auto">9.5 Archiving Procedures</h3>
<h4 data-heading="Retention Schedule" dir="auto">Retention Schedule</h4>
<p dir="auto"><strong>Permanent:</strong></p>
<ul>
<li dir="auto">Council minutes</li>
<li dir="auto">Property titles</li>
<li dir="auto">Historical records</li>
<li dir="auto">Baptism registers</li>
<li dir="auto">Marriage registers</li>
</ul>
<p dir="auto"><strong>7 Years:</strong></p>
<ul>
<li dir="auto">Financial records</li>
<li dir="auto">Tax documents</li>
<li dir="auto">Insurance policies</li>
<li dir="auto">Contracts</li>
</ul>
<p dir="auto"><strong>3 Years:</strong></p>
<ul>
<li dir="auto">General correspondence</li>
<li dir="auto">Event records</li>
<li dir="auto">Volunteer records</li>
<li dir="auto">Training records</li>
</ul>
<p dir="auto"><strong>1 Year:</strong></p>
<ul>
<li dir="auto">Bulletins</li>
<li dir="auto">Newsletters</li>
<li dir="auto">Promotional material</li>
<li dir="auto">Routine emails</li>
</ul>
<h4 data-heading="Archive Process" dir="auto">Archive Process</h4>
<ol>
<li dir="auto">
<p><strong>Preparation</strong></p>
<ul>
<li dir="auto">Sort by category</li>
<li dir="auto">Remove duplicates</li>
<li dir="auto">Label clearly</li>
<li dir="auto">Create index</li>
</ul>
</li>
<li dir="auto">
<p><strong>Storage</strong></p>
<ul>
<li dir="auto">Archive boxes</li>
<li dir="auto">Climate control</li>
<li dir="auto">Secure location</li>
<li dir="auto">Access log</li>
</ul>
</li>
<li dir="auto">
<p><strong>Disposal</strong></p>
<ul>
<li dir="auto">Follow schedule</li>
<li dir="auto">Secure destruction</li>
<li dir="auto">Document disposal</li>
<li dir="auto">Update index</li>
</ul>
</li>
</ol>
<hr>
<h2 data-heading="Part 10: Health &amp; Safety" dir="auto">Part 10: Health &amp; Safety</h2>
<h3 data-heading="10.1 Fire Evacuation Procedures" dir="auto">10.1 Fire Evacuation Procedures</h3>
<h4 data-heading="Evacuation Plan" dir="auto">Evacuation Plan</h4>
<p dir="auto"><strong>Alert Signal:</strong></p>
<ul>
<li dir="auto">Continuous alarm bell</li>
<li dir="auto">Verbal announcement</li>
<li dir="auto">Visual indicators</li>
</ul>
<p dir="auto"><strong>Assembly Points:</strong></p>
<ul>
<li dir="auto">Primary: Front car park</li>
<li dir="auto">Secondary: Rear garden</li>
<li dir="auto">Accessible: Side entrance</li>
</ul>
<h4 data-heading="Evacuation Roles" dir="auto">Evacuation Roles</h4>
<p dir="auto"><strong>Fire Wardens:</strong></p>
<ul>
<li dir="auto">Check all areas</li>
<li dir="auto">Assist evacuation</li>
<li dir="auto">Report to controller</li>
<li dir="auto">Liaise with services</li>
</ul>
<p dir="auto"><strong>Evacuation Controller:</strong></p>
<ul>
<li dir="auto">Coordinate evacuation</li>
<li dir="auto">Account for people</li>
<li dir="auto">Contact emergency services</li>
<li dir="auto">Authorize re-entry</li>
</ul>
<h4 data-heading="Evacuation Process" dir="auto">Evacuation Process</h4>
<ol>
<li dir="auto">
<p><strong>On Hearing Alarm:</strong></p>
<ul>
<li dir="auto">Stop immediately</li>
<li dir="auto">Leave belongings</li>
<li dir="auto">Exit via nearest route</li>
<li dir="auto">Assist others</li>
<li dir="auto">Go to assembly point</li>
</ul>
</li>
<li dir="auto">
<p><strong>At Assembly Point:</strong></p>
<ul>
<li dir="auto">Report to warden</li>
<li dir="auto">Stay together</li>
<li dir="auto">Await instructions</li>
<li dir="auto">Don't re-enter</li>
</ul>
</li>
<li dir="auto">
<p><strong>All Clear:</strong></p>
<ul>
<li dir="auto">Wait for authorization</li>
<li dir="auto">Return orderly</li>
<li dir="auto">Report issues</li>
<li dir="auto">Resume activities</li>
</ul>
</li>
</ol>
<h3 data-heading="10.2 Fire Equipment Testing" dir="auto">10.2 Fire Equipment Testing</h3>
<h4 data-heading="Monthly Checks" dir="auto">Monthly Checks</h4>
<p dir="auto"><strong>Visual Inspection:</strong></p>
<ul>
<li dir="auto">Extinguisher pressure</li>
<li dir="auto">Hose condition</li>
<li dir="auto">Exit signs working</li>
<li dir="auto">Emergency lights</li>
<li dir="auto">Alarm panels</li>
</ul>
<p dir="auto"><strong>Documentation:</strong></p>
<ul>
<li dir="auto">Record checks</li>
<li dir="auto">Note defects</li>
<li dir="auto">Report issues</li>
<li dir="auto">Schedule repairs</li>
</ul>
<h4 data-heading="Annual Testing" dir="auto">Annual Testing</h4>
<p dir="auto"><strong>Professional Service:</strong></p>
<ul>
<li dir="auto">Equipment testing</li>
<li dir="auto">Compliance certification</li>
<li dir="auto">Defect repairs</li>
<li dir="auto">Report filing</li>
</ul>
<p dir="auto"><strong>Training Update:</strong></p>
<ul>
<li dir="auto">Staff briefing</li>
<li dir="auto">Practical demonstration</li>
<li dir="auto">Evacuation drill</li>
<li dir="auto">Feedback session</li>
</ul>
<h3 data-heading="10.3 First Aid Procedures" dir="auto">10.3 First Aid Procedures</h3>
<h4 data-heading="First Aid Resources" dir="auto">First Aid Resources</h4>
<p dir="auto"><strong>First Aid Stations:</strong></p>
<ul>
<li dir="auto">Office: Full kit</li>
<li dir="auto">Kitchen: Burns kit</li>
<li dir="auto">Hall: Basic kit</li>
<li dir="auto">Vehicle: Travel kit</li>
</ul>
<p dir="auto"><strong>Emergency Equipment:</strong></p>
<ul>
<li dir="auto">Defibrillator (AED)</li>
<li dir="auto">Emergency blankets</li>
<li dir="auto">Spinal board</li>
<li dir="auto">Wheelchair</li>
</ul>
<h4 data-heading="Response Procedures" dir="auto">Response Procedures</h4>
<p dir="auto"><strong>Minor Injuries:</strong></p>
<ol>
<li dir="auto">Assess injury</li>
<li dir="auto">Provide treatment</li>
<li dir="auto">Record in register</li>
<li dir="auto">Advise further care</li>
</ol>
<p dir="auto"><strong>Serious Injuries:</strong></p>
<ol>
<li dir="auto">Call 000</li>
<li dir="auto">Provide first aid</li>
<li dir="auto">Don't move if spinal</li>
<li dir="auto">Comfort and reassure</li>
<li dir="auto">Guide ambulance</li>
</ol>
<h4 data-heading="First Aid Records" dir="auto">First Aid Records</h4>
<p dir="auto"><strong>Incident Register:</strong></p>
<ul>
<li dir="auto">Date and time</li>
<li dir="auto">Person details</li>
<li dir="auto">Injury description</li>
<li dir="auto">Treatment given</li>
<li dir="auto">First aider name</li>
<li dir="auto">Follow-up required</li>
</ul>
<h3 data-heading="10.4 Emergency Contacts" dir="auto">10.4 Emergency Contacts</h3>
<h4 data-heading="Emergency Services" dir="auto">Emergency Services</h4>
<ul>
<li dir="auto"><strong>Emergency (Police/Fire/Ambulance):</strong> 000</li>
<li dir="auto"><strong>Police (Non-urgent):</strong> 131 444</li>
<li dir="auto"><strong>SES:</strong> 132 500</li>
<li dir="auto"><strong>Poisons Information:</strong> 131 126</li>
</ul>
<h4 data-heading="Utilities" dir="auto">Utilities</h4>
<ul>
<li dir="auto"><strong>Electricity Emergency:</strong> [Provider specific]</li>
<li dir="auto"><strong>Gas Emergency:</strong> [Provider specific]</li>
<li dir="auto"><strong>Water Emergency:</strong> [Provider specific]</li>
</ul>
<h4 data-heading="Church Contacts" dir="auto">Church Contacts</h4>
<ul>
<li dir="auto"><strong>Ministry Team Leader:</strong> [Mobile]</li>
<li dir="auto"><strong>Church Council Chair:</strong> [Mobile]</li>
<li dir="auto"><strong>Property Officer:</strong> [Mobile]</li>
<li dir="auto"><strong>Administrator:</strong> [Mobile]</li>
</ul>
<blockquote dir="auto">
<p><strong>Note:</strong> Maintain current contact list separately. Update quarterly.</p>
</blockquote>
<h3 data-heading="10.5 Workplace Health &amp; Safety" dir="auto">10.5 Workplace Health &amp; Safety</h3>
<h4 data-heading="Risk Management" dir="auto">Risk Management</h4>
<p dir="auto"><strong>Regular Assessment:</strong></p>
<ul>
<li dir="auto">Identify hazards</li>
<li dir="auto">Assess risks</li>
<li dir="auto">Implement controls</li>
<li dir="auto">Monitor effectiveness</li>
<li dir="auto">Review regularly</li>
</ul>
<p dir="auto"><strong>Common Hazards:</strong></p>
<ul>
<li dir="auto">Slip/trip hazards</li>
<li dir="auto">Manual handling</li>
<li dir="auto">Electrical equipment</li>
<li dir="auto">Chemicals</li>
<li dir="auto">Working at height</li>
</ul>
<h4 data-heading="Safety Procedures" dir="auto">Safety Procedures</h4>
<p dir="auto"><strong>General Safety:</strong></p>
<ul>
<li dir="auto">Report hazards immediately</li>
<li dir="auto">Use equipment correctly</li>
<li dir="auto">Wear appropriate PPE</li>
<li dir="auto">Follow procedures</li>
<li dir="auto">Attend training</li>
</ul>
<p dir="auto"><strong>Specific Tasks:</strong></p>
<ul>
<li dir="auto">Ladder use: 2-person rule</li>
<li dir="auto">Lifting: Bend knees</li>
<li dir="auto">Chemicals: Read SDS</li>
<li dir="auto">Electrical: Tag and test</li>
<li dir="auto">Hot work: Permits required</li>
</ul>
<h4 data-heading="Incident Prevention" dir="auto">Incident Prevention</h4>
<p dir="auto"><strong>Workplace Inspections:</strong></p>
<ul>
<li dir="auto">Monthly walkthrough</li>
<li dir="auto">Hazard identification</li>
<li dir="auto">Action planning</li>
<li dir="auto">Progress monitoring</li>
</ul>
<p dir="auto"><strong>Safety Communication:</strong></p>
<ul>
<li dir="auto">Notice board updates</li>
<li dir="auto">Safety moments</li>
<li dir="auto">Training sessions</li>
<li dir="auto">Incident learnings</li>
</ul>
<hr>
<h2 data-heading="Part 11: Resources &amp; Supplies" dir="auto">Part 11: Resources &amp; Supplies</h2>
<h3 data-heading="11.1 Stationery Ordering" dir="auto">11.1 Stationery Ordering</h3>
<h4 data-heading="Inventory Management" dir="auto">Inventory Management</h4>
<p dir="auto"><strong>Stock Levels:</strong></p>
<ul>
<li dir="auto">Minimum levels set</li>
<li dir="auto">Weekly stock check</li>
<li dir="auto">Re-order points</li>
<li dir="auto">Usage tracking</li>
</ul>
<p dir="auto"><strong>Common Items:</strong></p>
<ul>
<li dir="auto">Paper (A4, A5)</li>
<li dir="auto">Envelopes (DL, C4)</li>
<li dir="auto">Pens and pencils</li>
<li dir="auto">Folders and files</li>
<li dir="auto">Printer cartridges</li>
<li dir="auto">Labels and tags</li>
</ul>
<h4 data-heading="Ordering Process" dir="auto">Ordering Process</h4>
<p dir="auto"><strong>Monthly Order:</strong></p>
<ol>
<li dir="auto">Check stock levels</li>
<li dir="auto">Review usage rates</li>
<li dir="auto">Prepare order list</li>
<li dir="auto">Obtain approval</li>
<li dir="auto">Place order</li>
<li dir="auto">Process invoice</li>
<li dir="auto">Check delivery</li>
<li dir="auto">Update inventory</li>
</ol>
<h4 data-heading="Preferred Suppliers" dir="auto">Preferred Suppliers</h4>
<p dir="auto"><strong>Primary Supplier:</strong></p>
<ul>
<li dir="auto">Monthly account</li>
<li dir="auto">Competitive pricing</li>
<li dir="auto">Next-day delivery</li>
<li dir="auto">Online ordering</li>
</ul>
<p dir="auto"><strong>Specialty Items:</strong></p>
<ul>
<li dir="auto">Church suppliers</li>
<li dir="auto">Local businesses</li>
<li dir="auto">Bulk suppliers</li>
</ul>
<h3 data-heading="11.2 Kitchen Supplies" dir="auto">11.2 Kitchen Supplies</h3>
<h4 data-heading="Supply Categories" dir="auto">Supply Categories</h4>
<p dir="auto"><strong>Consumables:</strong></p>
<ul>
<li dir="auto">Tea and coffee</li>
<li dir="auto">Sugar and milk</li>
<li dir="auto">Disposable cups</li>
<li dir="auto">Paper towels</li>
<li dir="auto">Cleaning products</li>
<li dir="auto">Dishwashing supplies</li>
</ul>
<p dir="auto"><strong>Equipment:</strong></p>
<ul>
<li dir="auto">Utensils</li>
<li dir="auto">Crockery</li>
<li dir="auto">Serving items</li>
<li dir="auto">Appliances</li>
</ul>
<h4 data-heading="Purchasing Guidelines" dir="auto">Purchasing Guidelines</h4>
<p dir="auto"><strong>Regular Supplies:</strong></p>
<ul>
<li dir="auto">Buy in bulk</li>
<li dir="auto">Compare prices</li>
<li dir="auto">Check use-by dates</li>
<li dir="auto">Rotate stock</li>
</ul>
<p dir="auto"><strong>Special Events:</strong></p>
<ul>
<li dir="auto">Plan requirements</li>
<li dir="auto">Order in advance</li>
<li dir="auto">Include contingency</li>
<li dir="auto">Track usage</li>
</ul>
<h3 data-heading="11.3 Cleaning Supplies" dir="auto">11.3 Cleaning Supplies</h3>
<h4 data-heading="Supply Requirements" dir="auto">Supply Requirements</h4>
<p dir="auto"><strong>Regular Stock:</strong></p>
<ul>
<li dir="auto">Disinfectants</li>
<li dir="auto">Floor cleaners</li>
<li dir="auto">Window cleaners</li>
<li dir="auto">Toilet cleaners</li>
<li dir="auto">Air fresheners</li>
<li dir="auto">Cleaning cloths</li>
<li dir="auto">Mops and buckets</li>
<li dir="auto">Vacuum bags</li>
</ul>
<h4 data-heading="Safety Requirements" dir="auto">Safety Requirements</h4>
<p dir="auto"><strong>Chemical Safety:</strong></p>
<ul>
<li dir="auto">Safety Data Sheets</li>
<li dir="auto">Proper storage</li>
<li dir="auto">Clear labeling</li>
<li dir="auto">PPE provision</li>
<li dir="auto">Spill kit</li>
<li dir="auto">Training records</li>
</ul>
<h3 data-heading="11.4 Gas Bottle Replacement" dir="auto">11.4 Gas Bottle Replacement</h3>
<h4 data-heading="Monitoring System" dir="auto">Monitoring System</h4>
<p dir="auto"><strong>Usage Tracking:</strong></p>
<ul>
<li dir="auto">Weekly checks</li>
<li dir="auto">Gauge readings</li>
<li dir="auto">Usage patterns</li>
<li dir="auto">Order timing</li>
</ul>
<h4 data-heading="Replacement Process" dir="auto">Replacement Process</h4>
<ol>
<li dir="auto">
<p><strong>Low Gas Alert</strong></p>
<ul>
<li dir="auto">Check gauge</li>
<li dir="auto">Estimate remaining</li>
<li dir="auto">Schedule delivery</li>
</ul>
</li>
<li dir="auto">
<p><strong>Ordering</strong></p>
<ul>
<li dir="auto">Contact supplier</li>
<li dir="auto">Confirm delivery date</li>
<li dir="auto">Arrange access</li>
<li dir="auto">Process payment</li>
</ul>
</li>
<li dir="auto">
<p><strong>Changeover</strong></p>
<ul>
<li dir="auto">Safety check</li>
<li dir="auto">Disconnect empty</li>
<li dir="auto">Connect full</li>
<li dir="auto">Test operation</li>
<li dir="auto">Update records</li>
</ul>
</li>
</ol>
<h3 data-heading="11.5 Printer &amp; Copier Supplies" dir="auto">11.5 Printer &amp; Copier Supplies</h3>
<h4 data-heading="Consumables" dir="auto">Consumables</h4>
<p dir="auto"><strong>Stock Items:</strong></p>
<ul>
<li dir="auto">Toner cartridges</li>
<li dir="auto">Paper (various)</li>
<li dir="auto">Staples</li>
<li dir="auto">Binding supplies</li>
</ul>
<h4 data-heading="Maintenance Supplies" dir="auto">Maintenance Supplies</h4>
<p dir="auto"><strong>Service Items:</strong></p>
<ul>
<li dir="auto">Cleaning kits</li>
<li dir="auto">Replacement parts</li>
<li dir="auto">Service contracts</li>
<li dir="auto">Emergency supplies</li>
</ul>
<h4 data-heading="Ordering Guidelines" dir="auto">Ordering Guidelines</h4>
<p dir="auto"><strong>Toner Management:</strong></p>
<ul>
<li dir="auto">Track page counts</li>
<li dir="auto">Monitor levels</li>
<li dir="auto">Order at 20%</li>
<li dir="auto">Keep one spare</li>
<li dir="auto">Recycle empties</li>
</ul>
<p dir="auto"><strong>Paper Stock:</strong></p>
<ul>
<li dir="auto">Standard: 10 reams minimum</li>
<li dir="auto">Special: As required</li>
<li dir="auto">Card stock: Project-based</li>
<li dir="auto">Color: Limited stock</li>
</ul>
<hr>
<h2 data-heading="Part 12: Technology &amp; Equipment" dir="auto">Part 12: Technology &amp; Equipment</h2>
<h3 data-heading="12.1 Computer Systems" dir="auto">12.1 Computer Systems</h3>
<h4 data-heading="System Management" dir="auto">System Management</h4>
<p dir="auto"><strong>Daily Tasks:</strong></p>
<ul>
<li dir="auto">System startup</li>
<li dir="auto">Login procedures</li>
<li dir="auto">Application launches</li>
<li dir="auto">Data backups</li>
</ul>
<p dir="auto"><strong>Weekly Tasks:</strong></p>
<ul>
<li dir="auto">System updates</li>
<li dir="auto">Virus scans</li>
<li dir="auto">Disk cleanup</li>
<li dir="auto">Performance check</li>
</ul>
<p dir="auto"><strong>Monthly Tasks:</strong></p>
<ul>
<li dir="auto">Full backup</li>
<li dir="auto">Software updates</li>
<li dir="auto">Password changes</li>
<li dir="auto">System optimization</li>
</ul>
<h4 data-heading="User Accounts" dir="auto">User Accounts</h4>
<p dir="auto"><strong>Account Types:</strong></p>
<ul>
<li dir="auto">Administrator: Full access</li>
<li dir="auto">Standard: Limited access</li>
<li dir="auto">Guest: Temporary access</li>
</ul>
<p dir="auto"><strong>Password Policy:</strong></p>
<ul>
<li dir="auto">Minimum 8 characters</li>
<li dir="auto">Include numbers/symbols</li>
<li dir="auto">Change quarterly</li>
<li dir="auto">No sharing</li>
<li dir="auto">Secure storage</li>
</ul>
<h3 data-heading="12.2 Printer &amp; Copier Operations" dir="auto">12.2 Printer &amp; Copier Operations</h3>
<h4 data-heading="Basic Operations" dir="auto">Basic Operations</h4>
<p dir="auto"><strong>Printing:</strong></p>
<ol>
<li dir="auto">Check paper levels</li>
<li dir="auto">Select correct tray</li>
<li dir="auto">Choose settings</li>
<li dir="auto">Print document</li>
<li dir="auto">Collect promptly</li>
</ol>
<p dir="auto"><strong>Copying:</strong></p>
<ol>
<li dir="auto">Place original</li>
<li dir="auto">Select options</li>
<li dir="auto">Enter quantity</li>
<li dir="auto">Start copy</li>
<li dir="auto">Remove originals</li>
</ol>
<p dir="auto"><strong>Scanning:</strong></p>
<ol>
<li dir="auto">Place document</li>
<li dir="auto">Select destination</li>
<li dir="auto">Choose quality</li>
<li dir="auto">Start scan</li>
<li dir="auto">Verify receipt</li>
</ol>
<h4 data-heading="Troubleshooting" dir="auto">Troubleshooting</h4>
<p dir="auto"><strong>Common Issues:</strong></p>
<p dir="auto"><strong>Paper Jam:</strong></p>
<ol>
<li dir="auto">Turn off machine</li>
<li dir="auto">Open all doors</li>
<li dir="auto">Remove paper carefully</li>
<li dir="auto">Check for tears</li>
<li dir="auto">Restart machine</li>
</ol>
<p dir="auto"><strong>Poor Quality:</strong></p>
<ol>
<li dir="auto">Clean glass</li>
<li dir="auto">Check toner</li>
<li dir="auto">Run cleaning cycle</li>
<li dir="auto">Adjust settings</li>
<li dir="auto">Call service if persistent</li>
</ol>
<h3 data-heading="12.3 Audio-Visual Equipment" dir="auto">12.3 Audio-Visual Equipment</h3>
<h4 data-heading="Equipment List" dir="auto">Equipment List</h4>
<p dir="auto"><strong>Sound System:</strong></p>
<ul>
<li dir="auto">Mixer console</li>
<li dir="auto">Microphones</li>
<li dir="auto">Speakers</li>
<li dir="auto">Cables and stands</li>
</ul>
<p dir="auto"><strong>Visual System:</strong></p>
<ul>
<li dir="auto">Projector</li>
<li dir="auto">Screen</li>
<li dir="auto">Laptop</li>
<li dir="auto">Cables and adapters</li>
</ul>
<h4 data-heading="Operation Procedures" dir="auto">Operation Procedures</h4>
<p dir="auto"><strong>Setup:</strong></p>
<ol>
<li dir="auto">Connect equipment</li>
<li dir="auto">Power on sequence</li>
<li dir="auto">Test audio levels</li>
<li dir="auto">Check visual display</li>
<li dir="auto">Adjust as needed</li>
</ol>
<p dir="auto"><strong>Pack Down:</strong></p>
<ol>
<li dir="auto">Power off sequence</li>
<li dir="auto">Disconnect cables</li>
<li dir="auto">Store properly</li>
<li dir="auto">Secure area</li>
<li dir="auto">Log any issues</li>
</ol>
<h3 data-heading="12.4 Backup Procedures" dir="auto">12.4 Backup Procedures</h3>
<h4 data-heading="Backup Schedule" dir="auto">Backup Schedule</h4>
<p dir="auto"><strong>Daily:</strong></p>
<ul>
<li dir="auto">Database backup</li>
<li dir="auto">Document changes</li>
<li dir="auto">Email archive</li>
</ul>
<p dir="auto"><strong>Weekly:</strong></p>
<ul>
<li dir="auto">Full system backup</li>
<li dir="auto">Off-site copy</li>
<li dir="auto">Verification test</li>
</ul>
<p dir="auto"><strong>Monthly:</strong></p>
<ul>
<li dir="auto">Archive creation</li>
<li dir="auto">Cloud sync</li>
<li dir="auto">Recovery test</li>
</ul>
<h4 data-heading="Backup Management" dir="auto">Backup Management</h4>
<p dir="auto"><strong>Storage Locations:</strong></p>
<ul>
<li dir="auto">Local: External drive</li>
<li dir="auto">Cloud: Secure service</li>
<li dir="auto">Off-site: Bank safe</li>
</ul>
<p dir="auto"><strong>Recovery Procedures:</strong></p>
<ol>
<li dir="auto">Identify loss</li>
<li dir="auto">Locate backup</li>
<li dir="auto">Restore data</li>
<li dir="auto">Verify integrity</li>
<li dir="auto">Document incident</li>
</ol>
<h3 data-heading="12.5 IT Support" dir="auto">12.5 IT Support</h3>
<h4 data-heading="Support Levels" dir="auto">Support Levels</h4>
<p dir="auto"><strong>Level 1 (Internal):</strong></p>
<ul>
<li dir="auto">Password resets</li>
<li dir="auto">Basic troubleshooting</li>
<li dir="auto">Software help</li>
<li dir="auto">Printer issues</li>
</ul>
<p dir="auto"><strong>Level 2 (Volunteer):</strong></p>
<ul>
<li dir="auto">Network issues</li>
<li dir="auto">Hardware problems</li>
<li dir="auto">System configuration</li>
<li dir="auto">Advanced troubleshooting</li>
</ul>
<p dir="auto"><strong>Level 3 (Professional):</strong></p>
<ul>
<li dir="auto">Server issues</li>
<li dir="auto">Security breaches</li>
<li dir="auto">Major failures</li>
<li dir="auto">Infrastructure</li>
</ul>
<h4 data-heading="Support Process" dir="auto">Support Process</h4>
<ol>
<li dir="auto">
<p><strong>Problem Identification</strong></p>
<ul>
<li dir="auto">Document issue</li>
<li dir="auto">Note error messages</li>
<li dir="auto">Check obvious causes</li>
<li dir="auto">Try basic fixes</li>
</ul>
</li>
<li dir="auto">
<p><strong>Escalation</strong></p>
<ul>
<li dir="auto">Internal attempt</li>
<li dir="auto">Call volunteer</li>
<li dir="auto">Contact professional</li>
<li dir="auto">Emergency procedures</li>
</ul>
</li>
<li dir="auto">
<p><strong>Resolution</strong></p>
<ul>
<li dir="auto">Implement fix</li>
<li dir="auto">Test thoroughly</li>
<li dir="auto">Document solution</li>
<li dir="auto">Train users</li>
</ul>
</li>
</ol>
<hr>
<h2 data-heading="Appendices" dir="auto">Appendices</h2>
<h3 data-heading="Appendix A: Forms &amp; Templates" dir="auto">Appendix A: Forms &amp; Templates</h3>
<h4 data-heading="Available Forms" dir="auto">Available Forms</h4>
<p dir="auto"><strong>Administration:</strong></p>
<ul>
<li dir="auto">Visitor card</li>
<li dir="auto">Message form</li>
<li dir="auto">Incident report</li>
<li dir="auto">Leave request</li>
<li dir="auto">Purchase order</li>
</ul>
<p dir="auto"><strong>Member Services:</strong></p>
<ul>
<li dir="auto">Membership application</li>
<li dir="auto">Baptism request</li>
<li dir="auto">Wedding booking</li>
<li dir="auto">Funeral arrangement</li>
<li dir="auto">Name badge order</li>
</ul>
<p dir="auto"><strong>Property:</strong></p>
<ul>
<li dir="auto">Hall hire application</li>
<li dir="auto">Booking request</li>
<li dir="auto">Key agreement</li>
<li dir="auto">Maintenance request</li>
<li dir="auto">Inspection checklist</li>
</ul>
<p dir="auto"><strong>Finance:</strong></p>
<ul>
<li dir="auto">Receipt book</li>
<li dir="auto">Petty cash voucher</li>
<li dir="auto">Reimbursement claim</li>
<li dir="auto">Donation envelope</li>
<li dir="auto">Banking slip</li>
</ul>
<p dir="auto"><strong>Volunteers:</strong></p>
<ul>
<li dir="auto">Application form</li>
<li dir="auto">Reference check</li>
<li dir="auto">Training record</li>
<li dir="auto">Roster template</li>
<li dir="auto">Recognition certificate</li>
</ul>
<blockquote dir="auto">
<p><strong>Note:</strong> All forms available in office or digital library</p>
</blockquote>
<h3 data-heading="Appendix B: Contact Lists" dir="auto">Appendix B: Contact Lists</h3>
<h4 data-heading="Service Providers" dir="auto">Service Providers</h4>
<p dir="auto"><strong>Utilities:</strong></p>
<ul>
<li dir="auto">Electricity</li>
<li dir="auto">Gas</li>
<li dir="auto">Water</li>
<li dir="auto">Internet</li>
<li dir="auto">Phone</li>
</ul>
<p dir="auto"><strong>Maintenance:</strong></p>
<ul>
<li dir="auto">Plumber</li>
<li dir="auto">Electrician</li>
<li dir="auto">Air conditioning</li>
<li dir="auto">Security</li>
<li dir="auto">Cleaning</li>
</ul>
<p dir="auto"><strong>Suppliers:</strong></p>
<ul>
<li dir="auto">Office supplies</li>
<li dir="auto">Kitchen supplies</li>
<li dir="auto">Cleaning supplies</li>
<li dir="auto">Church supplies</li>
</ul>
<p dir="auto"><strong>Professional Services:</strong></p>
<ul>
<li dir="auto">Accountant</li>
<li dir="auto">Solicitor</li>
<li dir="auto">Insurance</li>
<li dir="auto">Banking</li>
<li dir="auto">IT support</li>
</ul>
<blockquote dir="auto">
<p><strong>Note:</strong> Current contact details maintained separately in Contact Register</p>
</blockquote>
<h3 data-heading="Appendix C: Quick Reference Guides" dir="auto">Appendix C: Quick Reference Guides</h3>
<h4 data-heading="Common Tasks" dir="auto">Common Tasks</h4>
<p dir="auto"><strong>Answer Phone:</strong></p>
<ol>
<li dir="auto">Greeting</li>
<li dir="auto">Listen</li>
<li dir="auto">Record</li>
<li dir="auto">Action</li>
<li dir="auto">Follow-up</li>
</ol>
<p dir="auto"><strong>Process Visitor:</strong></p>
<ol>
<li dir="auto">Welcome</li>
<li dir="auto">Sign in</li>
<li dir="auto">Assist</li>
<li dir="auto">Record</li>
<li dir="auto">Follow-up</li>
</ol>
<p dir="auto"><strong>Handle Payment:</strong></p>
<ol>
<li dir="auto">Count</li>
<li dir="auto">Receipt</li>
<li dir="auto">Record</li>
<li dir="auto">Secure</li>
<li dir="auto">Bank</li>
</ol>
<p dir="auto"><strong>Book Hall:</strong></p>
<ol>
<li dir="auto">Check diary</li>
<li dir="auto">Quote</li>
<li dir="auto">Document</li>
<li dir="auto">Confirm</li>
<li dir="auto">Invoice</li>
</ol>
<p dir="auto"><strong>Order Supplies:</strong></p>
<ol>
<li dir="auto">Check stock</li>
<li dir="auto">List needs</li>
<li dir="auto">Get approval</li>
<li dir="auto">Order</li>
<li dir="auto">Receive</li>
</ol>
<h3 data-heading="Appendix D: Troubleshooting Common Issues" dir="auto">Appendix D: Troubleshooting Common Issues</h3>
<h4 data-heading="Office Equipment" dir="auto">Office Equipment</h4>
<p dir="auto"><strong>Computer Won't Start:</strong></p>
<ul>
<li dir="auto">Check power cable</li>
<li dir="auto">Try different outlet</li>
<li dir="auto">Hold power 10 seconds</li>
<li dir="auto">Call IT support</li>
</ul>
<p dir="auto"><strong>Printer Not Working:</strong></p>
<ul>
<li dir="auto">Check connection</li>
<li dir="auto">Restart printer</li>
<li dir="auto">Clear queue</li>
<li dir="auto">Check paper/toner</li>
<li dir="auto">Run diagnostics</li>
</ul>
<p dir="auto"><strong>Phone Issues:</strong></p>
<ul>
<li dir="auto">Check connection</li>
<li dir="auto">Restart handset</li>
<li dir="auto">Test different phone</li>
<li dir="auto">Check service status</li>
<li dir="auto">Call provider</li>
</ul>
<h4 data-heading="Facility Issues" dir="auto">Facility Issues</h4>
<p dir="auto"><strong>No Power:</strong></p>
<ul>
<li dir="auto">Check circuit breaker</li>
<li dir="auto">Test other outlets</li>
<li dir="auto">Check outside power</li>
<li dir="auto">Call electrician</li>
</ul>
<p dir="auto"><strong>No Water:</strong></p>
<ul>
<li dir="auto">Check main valve</li>
<li dir="auto">Check for leaks</li>
<li dir="auto">Test other taps</li>
<li dir="auto">Call plumber</li>
</ul>
<p dir="auto"><strong>Alarm Won't Set:</strong></p>
<ul>
<li dir="auto">Check all doors</li>
<li dir="auto">Check windows</li>
<li dir="auto">Check zones</li>
<li dir="auto">Call security</li>
</ul>
<h4 data-heading="Administrative Issues" dir="auto">Administrative Issues</h4>
<p dir="auto"><strong>Can't Find Document:</strong></p>
<ul>
<li dir="auto">Check filing system</li>
<li dir="auto">Search computer</li>
<li dir="auto">Check archives</li>
<li dir="auto">Ask colleagues</li>
<li dir="auto">Recreate if needed</li>
</ul>
<p dir="auto"><strong>System Error:</strong></p>
<ul>
<li dir="auto">Note error message</li>
<li dir="auto">Restart program</li>
<li dir="auto">Restart computer</li>
<li dir="auto">Check backups</li>
<li dir="auto">Call support</li>
</ul>
<p dir="auto"><strong>Locked Out:</strong></p>
<ul>
<li dir="auto">Check caps lock</li>
<li dir="auto">Try backup login</li>
<li dir="auto">Password reset</li>
<li dir="auto">Call administrator</li>
<li dir="auto">Emergency access</li>
</ul>
<hr>
<h2 data-heading="Document Control" dir="auto">Document Control</h2>
<p dir="auto"><strong>Version:</strong> 1.0<br>
<strong>Effective Date:</strong> [Current Date]<br>
<strong>Review Date:</strong> [Annual]<br>
<strong>Owner:</strong> Church Administrator<br>
<strong>Approved By:</strong> Church Council</p>
<p dir="auto"><strong>Revision History:</strong></p>
<ul>
<li dir="auto">Version 1.0: Initial comprehensive manual</li>
</ul>
<p dir="auto"><strong>Distribution:</strong></p>
<ul>
<li dir="auto">Church Administrator (Master)</li>
<li dir="auto">Office Volunteers (Reference)</li>
<li dir="auto">Ministry Team (Reference)</li>
<li dir="auto">Church Council (Reference)</li>
<li dir="auto">Website Dashboard (Digital)</li>
</ul>
<hr>
<p dir="auto"><em>This manual is a living document and should be updated regularly to reflect current practices and procedures. Suggestions for improvements should be submitted to the Church Administrator.</em></p>
<p dir="auto"><strong>© Caboolture Region Uniting Church Australia</strong><br>
<em>This manual is for internal use only and should not be distributed outside the church without authorization.</em></p>
    </body>
</html>