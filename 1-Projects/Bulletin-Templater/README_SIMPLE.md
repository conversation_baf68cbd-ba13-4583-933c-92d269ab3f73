# Church Bulletin Generator
*Table of Contents*
1. [[#✨ What It Does|✨ What It Does]]
2. [[#🚀 Quick Setup (5 minutes)|🚀 Quick Setup (5 minutes)]]
	1. [[#🚀 Quick Setup (5 minutes)#1. Install Dependencies|1. Install Dependencies]]
	2. [[#🚀 Quick Setup (5 minutes)#2. Setup Project|2. Setup Project]]
	3. [[#🚀 Quick Setup (5 minutes)#3. Initialize Database|3. Initialize Database]]
	4. [[#🚀 Quick Setup (5 minutes)#4. Check Everything Works|4. Check Everything Works]]
3. [[#📋 Basic Usage|📋 Basic Usage]]
	1. [[#📋 Basic Usage#Generate Your First Bulletin|Generate Your First Bulletin]]
	2. [[#📋 Basic Usage#Add Church Events|Add Church Events]]
	3. [[#📋 Basic Usage#Generate with Events Included|Generate with Events Included]]
4. [[#📁 What You Get|📁 What You Get]]
5. [[#🎯 Perfect For|🎯 Perfect For]]
6. [[#🔧 All Commands|🔧 All Commands]]
7. [[#💡 Pro Tips|💡 Pro Tips]]
8. [[#🆘 Need Help?|🆘 Need Help?]]
---
# Church Bulletin Generator

🎯 **Automated weekly church bulletin creation from templates and database content**

Transform your bulletin creation from hours of manual work to minutes of automated generation while preserving your exact design and formatting.

## ✨ What It Does

- 📅 **Auto-populates** bulletins with dates, service times, and preacher names
- 📊 **Manages** annual church data (events, Bible verses, contacts)
- 🎨 **Preserves** your existing bulletin design perfectly
- ⚡ **Generates** bulletins in seconds instead of hours
- 📱 **Ready** for future web interface integration

## 🚀 Quick Setup (5 minutes)

### 1. Install Dependencies
```bash
pip3 install click rich python-dateutil
```

### 2. Setup Project
```bash
python3 setup.py
```

### 3. Initialize Database
```bash
python3 main.py init-db
```

### 4. Check Everything Works
```bash
python3 main.py status
```

## 📋 Basic Usage

### Generate Your First Bulletin
```bash
# Generate bulletin for next Sunday
python3 main.py generate --date 2025-08-17

# Output: bulletin_20250817.svg in the output/ folder
```

### Add Church Events
```bash
# Add an event
python3 main.py add-event --date 2025-08-17 --title "Youth Group" --time "6:00pm"

# View events
python3 main.py list-events --date 2025-08-17
```

### Generate with Events Included
```bash
python3 main.py generate --date 2025-08-17
```

## 📁 What You Get

```
📂 Your Project/
├── 📄 bulletin_20250817.svg    # Generated bulletin
├── 📊 data/bulletin.db         # Your church data
├── 🎨 templates/               # Your bulletin template
└── 📤 output/                  # Generated files
```

## 🎯 Perfect For

- **Church Secretaries** - Reduce weekly bulletin prep from hours to minutes
- **Pastors** - Focus on content, not formatting
- **Volunteers** - Simple commands anyone can learn
- **Tech Teams** - Clean Python code ready for web integration

## 🔧 All Commands

```bash
# System
python3 main.py status              # Check system health
python3 main.py init-db             # Setup database

# Content Management  
python3 main.py add-event --date YYYY-MM-DD --title "Event"
python3 main.py list-events --date YYYY-MM-DD

# Bulletin Generation
python3 main.py generate --date YYYY-MM-DD --format svg
```

## 💡 Pro Tips

- **Use Sundays**: `--date 2025-08-17` (bulletins work best with Sunday dates)
- **Batch Events**: Add multiple events for the same date
- **SVG Output**: Works perfectly, PDF requires additional setup
- **Future Ready**: Designed for easy web interface addition

## 🆘 Need Help?

1. **Check Status**: `python3 main.py status`
2. **View Events**: `python3 main.py list-events`
3. **Regenerate**: Delete output file and run generate again

---

*Built for churches, by developers who understand ministry needs* 🙏
