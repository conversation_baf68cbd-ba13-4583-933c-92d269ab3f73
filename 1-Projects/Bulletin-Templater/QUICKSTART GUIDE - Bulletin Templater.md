# Church Bulletin Generator
*Table of Contents*
1. [[#🚀 Get Running in 5 Minutes|🚀 Get Running in 5 Minutes]]
	1. [[#🚀 Get Running in 5 Minutes#Step 1: Install Python Dependencies|Step 1: Install Python Dependencies]]
	2. [[#🚀 Get Running in 5 Minutes#Step 2: Setup the Project|Step 2: Setup the Project]]
	3. [[#🚀 Get Running in 5 Minutes#Step 3: Initialize Database|Step 3: Initialize Database]]
	4. [[#🚀 Get Running in 5 Minutes#Step 4: Test the System|Step 4: Test the System]]
2. [[#🎯 Your First Bulletin|🎯 Your First Bulletin]]
	1. [[#🎯 Your First Bulletin#Add Some Sample Data|Add Some Sample Data]]
	2. [[#🎯 Your First Bulletin#Generate the Bulletin|Generate the Bulletin]]
	3. [[#🎯 Your First Bulletin#Check Your Output|Check Your Output]]
3. [[#📅 Weekly Workflow|📅 Weekly Workflow]]
	1. [[#📅 Weekly Workflow#Every Week:|Every Week:]]
4. [[#🔧 Common Commands|🔧 Common Commands]]
5. [[#💡 Tips|💡 Tips]]
6. [[#🆘 Troubleshooting|🆘 Troubleshooting]]
---
# Quick Start Guide

## 🚀 Get Running in 5 Minutes

### Step 1: Install Python Dependencies
```bash
pip3 install click rich python-dateutil
```

### Step 2: Setup the Project
```bash
python3 setup.py
```

### Step 3: Initialize Database
```bash
python3 main.py init-db
```

### Step 4: Test the System
```bash
python3 main.py status
```

## 🎯 Your First Bulletin

### Add Some Sample Data
```bash
# Add an event for next Sunday
python3 main.py add-event --date 2025-08-17 --title "Youth Group Meeting" --time "6:00pm" --location "Church Hall"

# Add another event
python3 main.py add-event --date 2025-08-17 --title "Bible Study" --time "7:30pm" --description "Study of Romans chapter 8"
```

### Generate the Bulletin
```bash
python3 main.py generate --date 2025-08-17
```

### Check Your Output
```bash
# Your bulletin is now in: output/bulletin_20250817.svg
# Open it with any SVG viewer or web browser
```

## 📅 Weekly Workflow

### Every Week:
1. **Add new events**: `python3 main.py add-event --date YYYY-MM-DD --title "Event"`
2. **Generate bulletin**: `python3 main.py generate --date YYYY-MM-DD`
3. **Open the SVG file** in your browser or design software
4. **Print or distribute** as needed

## 🔧 Common Commands

```bash
# Check what events are scheduled
python3 main.py list-events --date 2025-08-17

# Generate for today
python3 main.py generate --date $(date +%Y-%m-%d)

# Generate for next Sunday (if today is Monday-Saturday)
python3 main.py generate --date 2025-08-17
```

## 💡 Tips

- **Always use Sunday dates** for bulletins (YYYY-MM-DD format)
- **Add events throughout the week** as they come up
- **The system automatically includes** service times and contact info
- **SVG files can be opened** in web browsers, Inkscape, or Adobe Illustrator

## 🆘 Troubleshooting

**Problem**: Command not found
**Solution**: Make sure you're in the project directory

**Problem**: No bulletin generated
**Solution**: Check `python3 main.py status` and ensure database is initialized

**Problem**: Empty bulletin
**Solution**: Add some events first, then regenerate

---

**You're ready to go!** 🎉
