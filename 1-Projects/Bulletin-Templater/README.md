---
creation_date: 2025-08-14
modification_date: 2025-08-14
type: resource
source: 
tags: [para/resources]
---
# Church Bulletin Generator
*Table of Contents*
1. [[#Features|Features]]
2. [[#Prerequisites|Prerequisites]]
3. [[#Installation|Installation]]
	1. [[#Installation#Step 1: <PERSON>lone the Repository|Step 1: <PERSON>lone the Repository]]
	2. [[#Installation#Step 2: (Optional) Python Version Management|Step 2: (Optional) Python Version Management]]
		1. [[#Step 2: (Optional) Python Version Management#macOS (using pyenv)|macOS (using pyenv)]]
		2. [[#Step 2: (Optional) Python Version Management#Windows (using pyenv-win)|Windows (using pyenv-win)]]
	3. [[#Installation#Step 3: Create Virtual Environment|Step 3: Create Virtual Environment]]
		1. [[#Step 3: Create Virtual Environment#macOS/Linux|macOS/Linux]]
		2. [[#Step 3: Create Virtual Environment#Windows|Windows]]
	4. [[#Installation#Step 4: Install Dependencies|Step 4: Install Dependencies]]
	5. [[#Installation#Step 5: Run Setup|Step 5: Run Setup]]
	6. [[#Installation#Step 6: Initialize Database|Step 6: Initialize Database]]
4. [[#Managing the Virtual Environment|Managing the Virtual Environment]]
	1. [[#Managing the Virtual Environment#Deactivating the Virtual Environment|Deactivating the Virtual Environment]]
	2. [[#Managing the Virtual Environment#Reactivating the Virtual Environment|Reactivating the Virtual Environment]]
		1. [[#Reactivating the Virtual Environment#macOS/Linux|macOS/Linux]]
		2. [[#Reactivating the Virtual Environment#Windows|Windows]]
	3. [[#Managing the Virtual Environment#Updating Dependencies|Updating Dependencies]]
	4. [[#Managing the Virtual Environment#Freezing Dependencies|Freezing Dependencies]]
5. [[#Quick Start|Quick Start]]
	1. [[#Quick Start#Check System Status|Check System Status]]
	2. [[#Quick Start#Generate a Bulletin|Generate a Bulletin]]
	3. [[#Quick Start#Manage Events|Manage Events]]
6. [[#License|License]]
7. [[#Project Structure|Project Structure]]
8. [[#Database Schema|Database Schema]]
9. [[#Template System|Template System]]
10. [[#Configuration|Configuration]]
11. [[#Future Development|Future Development]]
12. [[#Support|Support]]
13. [[#License|License]]
14. [[#Details|Details]]
15. [[#Examples|Examples]]
16. [[#Related Projects|Related Projects]]
17. [[#Related Areas|Related Areas]]
18. [[#Related|Related]]

---
# Overview

An automated system for generating weekly church bulletins from templates and database content.
## Features

- **Automated Content Generation**: Automatically populate bulletins with date-based content
- **Database Management**: Store annual data including Bible verses, events, service times, and contact information
- **Template System**: Use SVG templates that preserve exact formatting and design
- **Multiple Output Formats**: Generate PDF, SVG, and PNG formats
- **Command Line Interface**: Easy-to-use CLI for all operations
- **Future Web Interface**: Designed with JavaScript web interface integration in mind

# Key Installation Instructions
## Prerequisites

- Python 3.8 or higher
- pip (Python package manager)
- Git (for cloning the repository)

## Installation

### Step 1: Clone the Repository

```bash
git clone https://github.com/fjord-an/cruca-bulletin.git
cd cruca-bulletin
```

### Step 2: (Optional) Python Version Management

If you need to manage multiple Python versions, you can use a version manager:

#### macOS (using pyenv)

```bash
# Install pyenv via Homebrew
brew install pyenv

# Add pyenv to your shell
echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.zshrc
echo 'command -v pyenv >/dev/null || export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.zshrc
echo 'eval "$(pyenv init -)"' >> ~/.zshrc

# Restart your shell
source ~/.zshrc

# Install Python 3.11 (recommended)
pyenv install 3.11.9
pyenv local 3.11.9
```

#### Windows (using pyenv-win)

```powershell
# Install pyenv-win via PowerShell
Invoke-WebRequest -UseBasicParsing -Uri "https://raw.githubusercontent.com/pyenv-win/pyenv-win/master/pyenv-win/install-pyenv-win.ps1" -OutFile "./install-pyenv-win.ps1"; .\install-pyenv-win.ps1

# Restart PowerShell and install Python
pyenv install 3.11.9
pyenv local 3.11.9
```

Alternatively, for Windows users, you can use the official Python installer from [python.org](https://www.python.org/downloads/).

### Step 3: Create Virtual Environment

It's recommended to use a virtual environment to isolate project dependencies:

#### macOS/Linux

```bash
# Create virtual environment named 'cruca-bulletin'
python3 -m venv cruca-bulletin

# Activate the virtual environment
source cruca-bulletin/bin/activate
```

#### Windows

```powershell
# Create virtual environment named 'cruca-bulletin'
python -m venv cruca-bulletin

# Activate the virtual environment
# For Command Prompt:
cruca-bulletin\Scripts\activate.bat

# For PowerShell:
cruca-bulletin\Scripts\Activate.ps1
```

### Step 4: Install Dependencies

With your virtual environment activated:

```bash
# Upgrade pip to the latest version
pip install --upgrade pip

# Install project dependencies
pip install -r requirements.txt
```

### Step 5: Run Setup

```bash
python setup.py
```

### Step 6: Initialize Database

```bash
python main.py init-db
```

## Managing the Virtual Environment

### Deactivating the Virtual Environment

When you're done working on the project:

```bash
deactivate
```

### Reactivating the Virtual Environment

When you return to work on the project:

#### macOS/Linux
```bash
source cruca-bulletin/bin/activate
```

#### Windows
```powershell
# Command Prompt:
cruca-bulletin\Scripts\activate.bat

# PowerShell:
cruca-bulletin\Scripts\Activate.ps1
```

### Updating Dependencies

To update all packages to their latest compatible versions:

```bash
pip install --upgrade -r requirements.txt
```

### Freezing Dependencies

If you add new packages to the project:

```bash
pip freeze > requirements.txt
```

## Quick Start

### Check System Status
```bash
python main.py status
```

### Generate a Bulletin
```bash
# Generate for today
python main.py generate

# Generate for specific date
python main.py generate --date 2025-08-10

# Generate in different format
python main.py generate --date 2025-08-10 --format pdf
```

### Manage Events
```bash
# Add an event
python main.py add-event --date 2025-08-10 --title "Special Service" --time "7:00pm"

# List events
python main.py list-events --date 2025-08-10
```

## License

This project is developed for church use and may be adapted for similar organizations.

## Project Structure

```
bulletin_generator/
├── core/
│   ├── database.py          # Database operations
│   ├── template_parser.py   # SVG template parsing
│   └── content_generator.py # Content generation engine
├── models/
│   ├── bulletin.py          # Bulletin data model
│   ├── service.py           # Service time model
│   ├── event.py             # Event model
│   ├── bible_verse.py       # Bible verse model
│   └── contact.py           # Contact model
├── utils/
│   ├── date_utils.py        # Date utilities
│   ├── file_utils.py        # File utilities
│   └── validation.py        # Validation functions
└── config.py                # Configuration settings

templates/                   # SVG template files
output/                     # Generated bulletin files
data/                       # Database files
```

## Database Schema

The system uses SQLite with the following main tables:

- **service_times**: Service schedules and preachers
- **events**: Church events and announcements  
- **bible_verses**: Weekly Bible verses and themes
- **contacts**: Ministry team contact information
- **bulletin_metadata**: Bulletin titles and metadata

## Template System

The system uses SVG templates that preserve exact formatting. The template parser:

- Identifies text elements that can be replaced
- Maps content to specific locations and times
- Maintains professional design and layout
- Supports multiple output formats

## Configuration

Key settings in `bulletin_generator/config.py`:

- **Database path**: Location of SQLite database
- **Template directory**: SVG template files
- **Output directory**: Generated bulletin files
- **Service locations**: Default church locations and times
- **Contact information**: Default ministry contacts

## Future Development

This system is designed to integrate with a JavaScript web interface for:

- User-friendly data entry forms
- Visual bulletin preview
- Drag-and-drop template editing
- Online bulletin distribution

## Support

For technical support or questions about the system, contact the development team or refer to the project documentation.

## License

This project is developed by Jordan Pacey (fjord-an) for church use and may be adapted for similar church organisations.

## Examples
<!-- Examples or code snippets if applicable -->
``` TODO
// CLI examples coming soon
```

## Related Projects
<!-- Links to related projects -->
```dataview
LIST
FROM [[README]] AND "1-Projects"
```

## Related Areas
<!-- Links to related areas -->
```dataview
LIST
FROM [[README]] AND "2-Areas"
```


## Related
- [[3-Resources]]

