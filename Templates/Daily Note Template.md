---
creation_date: <% tp.date.now("YYYY-MM-DD HH:mm") %>
modification_date: <% tp.date.now("dddd Do MMMM YYYY HH:mm:ss") %>
type: daily
date: <% tp.date.now("YYYY-MM-DD") %>
day_of_week: <% tp.date.now("dddd") %>
week: <% tp.date.now("YYYY-[W]WW") %>
month: <% tp.date.now("YYYY-MM") %>
tags: [daily, <% tp.date.now("YYYY-MM") %>, cruca]
---

# <% tp.date.now("YYYY-MM-DD") %> - <% tp.date.now("dddd") %>

> **🔄 Template not processed?** Click here: [Process Template](obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater)

## Tasks for Today
#todo #tasks #outstanding 
- [ ] 
- [ ] 
- [ ] 

## Church Matters
<!-- Church-related activities, meetings, pastoral care -->
- 

## Hall Hire
<!-- Hall bookings, inquiries, maintenance -->
- 

## Administration
<!-- Administrative tasks, correspondence, documentation -->
- 

## Finance & Banking
<!-- Banking, donations, payments, reconciliation -->
- 

## Safe Church
<!-- Compliance, training, documentation -->
- 

## Follow-ups
<!-- Items requiring follow-up action -->
- [ ] 
- [ ] 

## Journal
<!-- How was your day? What happened? What did you learn? -->
- 

## Notes
<!-- Any other notes or information -->
- 

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(<% tp.date.now("YYYY-MM-DD") %>)
SORT time ASC
```

## Today's Tasks
```tasks
not done
due on <% tp.date.now("YYYY-MM-DD") %>
```

## Upcoming Tasks
```tasks
not done
due after <% tp.date.now("YYYY-MM-DD") %>
due before <% tp.date.now("YYYY-MM-DD", 7) %>
```

## Create New Note
- [[<% tp.date.now("YYYY-MM-DD") %> Meeting|Create New Meeting]]
- [[<% tp.date.now("YYYY-MM-DD") %> Task|Create New Task]]
- [[<% tp.date.now("YYYY-MM-DD") %> Hall Hire|Create Hall Hire Booking]]

## Related
- [[Daily Notes TOC]]
- [[<% tp.date.now("YYYY-MM") %>|Monthly Overview]]
- [[<% tp.date.now("YYYY-[W]WW") %>|Weekly Overview]]
- [[Comprehensive Administrative Procedures Guide]]
- [[Home]]
