---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: area
status: active
area: <% await tp.system.prompt("Area Category", "administration") %>
area_owner: <% await tp.system.prompt("Area Owner", "Jordan") %>
responsibility_level: <% await tp.system.prompt("Responsibility Level", "medium", "high, medium, low") %>
review_frequency: <% await tp.system.prompt("Review Frequency", "monthly", "daily, weekly, monthly, quarterly") %>
tags: [para/areas, <% await tp.system.prompt("Additional Tags (comma-separated)", "administration") %>]
related: []
organization: <% await tp.system.prompt("Organization (if applicable)", "") %>
key_contacts: []
last_review_date: <% tp.date.now("YYYY-MM-DD") %>
next_review_date: <% tp.date.now("YYYY-MM-DD", 30) %>
---

# <% tp.file.title %>

## Overview
<!-- Brief description of this area of responsibility -->

## Current Focus
<!-- What's the current focus in this area? -->
- 

## Key Responsibilities
<!-- List the key responsibilities in this area -->
- 

## Regular Tasks
<!-- Recurring tasks in this area -->
### Daily
- [ ] 

### Weekly
- [ ] 

### Monthly
- [ ] 

### Quarterly
- [ ] 

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE area = "<% await tp.system.prompt("Area Category", "administration") %>" AND (status = "active" OR !contains(status, "completed"))
SORT priority ASC, deadline ASC
```

## Related Resources
```dataview
TABLE WITHOUT ID
  file.link as "Resource",
  source as "Source",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE area = "<% await tp.system.prompt("Area Category", "administration") %>"
SORT file.mtime DESC
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  file.mtime as "Last Modified"
FROM -"Templates"
WHERE contains(file.content, "[[<% tp.file.title %>]]")
SORT file.mtime DESC
LIMIT 5
```

## Key Metrics
<!-- Metrics to track in this area -->
- 

## Create Related Notes
- [[<% tp.file.title %> Project|Create New Project]]
- [[<% tp.file.title %> Resource|Create New Resource]]
- [[<% tp.file.title %> Meeting <% tp.date.now("YYYY-MM-DD") %>|Create Meeting Note]]

## Related
- [[2-Areas]]
- [[Areas TOC]]
- [[<% await tp.system.prompt("Related Area TOC", "Administration TOC") %>]]
