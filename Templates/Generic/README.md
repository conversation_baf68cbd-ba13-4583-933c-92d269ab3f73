---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: resource
source: Internal documentation
tags: [para/resources, templates, guide, obsidian, metadata]
---

# Enhanced Templates Guide

This guide explains how to use the enhanced templates and scripts to create powerful, interconnected notes with rich metadata.

## Available Templates

### Enhanced Daily Template
- **File**: `Enhanced Daily.md`
- **Purpose**: Create comprehensive daily notes with improved metadata and dataview queries
- **Key Features**: 
  - Enhanced metadata for better searching
  - Quick capture section
  - Active projects view
  - Today's meetings view
  - Today's and upcoming tasks
  - Recent notes view
  - Quick links to create related notes

### Enhanced Project Template
- **File**: `Enhanced Project.md`
- **Purpose**: Create detailed project notes with comprehensive metadata
- **Key Features**:
  - Rich metadata including project owner, client, completion percentage
  - Success criteria section
  - Timeline with milestones
  - Related areas and resources views
  - Meeting notes view
  - Progress updates section
  - Quick links to create related notes

### Enhanced Area Template
- **File**: `Enhanced Area.md`
- **Purpose**: Create detailed area notes for ongoing responsibilities
- **Key Features**:
  - Rich metadata including area owner, responsibility level, review frequency
  - Key responsibilities section
  - Regular tasks organized by frequency
  - Active projects view
  - Related resources view
  - Recent notes view
  - Quick links to create related notes

### Enhanced Resource Template
- **File**: `Enhanced Resource.md`
- **Purpose**: Create detailed resource notes with comprehensive metadata
- **Key Features**:
  - Rich metadata including source, difficulty, keywords
  - Use cases section
  - Related projects and areas views
  - Related resources view
  - Quick links to create related notes

### Meeting Template
- **File**: `Meeting.md`
- **Purpose**: Create detailed meeting notes that link to related projects and areas
- **Key Features**:
  - Rich metadata including participants, location, project, area
  - Action items with assignees and due dates
  - Follow-up section
  - Related projects and areas views

### Person Template
- **File**: `Person.md`
- **Purpose**: Create detailed contact notes with comprehensive metadata
- **Key Features**:
  - Rich metadata including organization, role, relationship
  - Interactions log
  - Related projects and areas views
  - Meetings view
  - Quick links to create related notes

## Utility Scripts

### Template Selector
- **File**: `Template Selector.md`
- **Purpose**: Helps you select the appropriate template based on what you're trying to create
- **Usage**: Run this script when you want to create a new note and it will guide you through the process

### Create Related Note
- **File**: `Create Related Note.md`
- **Purpose**: Creates a new note that's automatically linked to the current note
- **Usage**: Run this script from any note to create a related note that will be automatically linked in both directions

### Enhance Metadata
- **File**: `Enhance Metadata.md`
- **Purpose**: Helps you enhance the metadata of existing notes
- **Usage**: Run this script from any note to add or update metadata fields based on the note type

## How to Use These Templates

### Using the Template Selector
1. Create a new note
2. Open the command palette (Ctrl+P)
3. Type "Templater: Open Template Selector"
4. Select "Template Selector"
5. Follow the prompts to select a template and create your note

### Using the Create Related Note Script
1. Open an existing note
2. Open the command palette (Ctrl+P)
3. Type "Templater: Open Template"
4. Select "Create Related Note"
5. Follow the prompts to create a related note that will be automatically linked

### Using the Enhance Metadata Script
1. Open an existing note
2. Open the command palette (Ctrl+P)
3. Type "Templater: Open Template"
4. Select "Enhance Metadata"
5. Follow the prompts to enhance the metadata of the current note

## Required Plugins
- **Templater**: For template functionality and scripts
- **Dataview**: For dynamic content queries
- **Tasks**: For task management

## Tips for Effective Note-Taking
1. **Use rich metadata**: The more metadata you add, the easier it will be to find and relate notes
2. **Create links between notes**: Use the Create Related Note script to create bidirectional links
3. **Use consistent tags**: Follow the tagging conventions in the templates
4. **Update metadata regularly**: Use the Enhance Metadata script to keep your metadata current
5. **Review your notes**: Use the dataview queries to review and maintain your notes

## Customizing Templates
Feel free to customize these templates to better suit your needs. Here are some tips:
- Add or remove metadata fields based on what's important to you
- Modify the dataview queries to show different information
- Add new sections that are relevant to your workflow
- Create new templates for specific types of notes

## Related
- [[Home]]
- [[PARA System Guide]]
- [[Dataview Guide]]
