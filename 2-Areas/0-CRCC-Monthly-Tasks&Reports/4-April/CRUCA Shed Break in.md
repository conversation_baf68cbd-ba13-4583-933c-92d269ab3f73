---
date: 07/04/2025
location: Caboolture
tags:
  - admin
  - crime
  - shed
  - tools
  - police
  - stolen
  - break
  - inbox
links: 
priority: "1"
---
---
creation_date: 2025-04-07
modification_date: 2025-04-07
type: index
aliases: [Resources]
tags: [para/resources, index]
---
# <PERSON>’s report of scene clues 

1. <PERSON> said that there was a screwdriver found in the garden and was taken home to the minister’s house to have a look at. 
2. The screwdriver was a rubber so was doubtful to have fingerprints on it but it could’ve been the tool of The breaking.
3. The roller door Lock might have been opened with the screwdriver unsure yet (not tested the lock). 
4. There was a leaf blower by the bins and a garden edger there too, (unsure if it was inside the garage or outside but it was towards the bin area so might’ve been inside the garage)
5. This might be why <PERSON> would’ve thought that they went through that roller door and they’ve taken that stuff there at the roller door near the bins. 
6. <PERSON> thought it was unlikely that they would have gone through the side door because they would not have known how to open the Pool fence gate as it blends in, appearing as a fence. The side roller door is only accessable from the back yard, through the gate/fence.
7. the perpetrator would require knowledge of the church building to know how to open the 2+ meter high pool fence. It is likely they would have opened it before the event. You cannot see the latch from the outside.
8. It is therefore, unlikely the side door was opened. We don’t have a key to get in the front roller door. Only the side roller door has a key.
---
creation_date: 2025-04-05
modification_date: 2025-04-05
type: note
aliases:
  - Routine
  - Areas
tags:
  - Note
  - Inbox
area: Responsibilities
project: Routines
resource: Todoist
archive: 
status: active
priority: "1"
links: 
related:
---
---
creation_date: 2025-04-07
modification_date: 2025-04-07
type: note
aliases:
  - Routine
  - Areas
tags:
  - Note
  - Inbox
area: Responsibilities
project: Routines
resource: Todoist
archive: 
status: active
priority: "1"
links: 
related:
---
Checkout airflow 
## Note
Checkout airflow for aut scheduling of data scraping and transformation with any python or script

## References



## Tasks
- [ ] 

## Metadata
- **Original Creation**: 2025-04-05
- **Source**: 
- **Context**:
## This Folder will contain all "quick notes" and "daily Notes." 
These must be sent to todoist and organised via links and metadata for search later.

## References
[[Brain/0-Daily Notes/Daily Note Folder. Must be cleaned Daily|Areas]]


## Tasks
- [ ] 

## Metadata
- **Original Creation**: 2025-04-05
- **Source**: 
- **Context**:---
creation_date: 2025-04-07
modification_date: 2025-04-07
type: index
aliases: [Resources]
tags: [para/resources, index]
---

# Resources

> Resources are topics or themes of ongoing interest that may be referenced across projects and areas.

## Knowledge Resources
- [[References]]
- [[Books]]
- [[Courses]]
- [[Articles]]

## Tool Resources
- [[Templates]]
- [[Checklists]]
- [[Software]]

## Resource Map
```dataview
TABLE 
  resource as "Resource",
  type as "Type"
FROM #para/resources
SORT resource ASC
```

## Related
- [[1-Projects]]
- [[2-Areas]]
- [[4-Archive]] 
##
- [ ] 


###
- 
[[../../create a link|create a link]]
[[CRUCA-Church-Vault/Events&Meeting Dates 1/Caboolture worship center meeting for events]]
[[Recess easter 1]]
[[CRUCA-Church-Vault/Events&Meeting Dates 1/Event List]]
