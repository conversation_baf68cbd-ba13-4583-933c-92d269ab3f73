---
tags: [admin, documentation, passwords, security]
---

# Password Management

## Overview
Password management for CRUCA is handled through two primary password managers:
- [[<PERSON>warden]] - Primary password manager for most accounts
- [[Proton Pass]] - Secondary password manager for highly sensitive accounts

## Bitwarden
Bitwarden serves as our primary password management solution, handling:
- General account passwords
- 2-factor authentication keys
- Passkey features (hardware key emulation)

### Setup Requirements
1. Install Bitwarden browser extension:
   - [[Chrome Web Store]]
   - [[Firefox Add-ons]]
   - [[Microsoft Edge Add-ons]]

### Features
- Passkey support
- 2FA management
- Secure password generation
- Cross-platform synchronisation

## Proton Pass
Proton Pass is used for highly sensitive accounts, including:
- Main office email credentials (<EMAIL>)
- Bitwarden master credentials
- Other critical system access

## Security Measures
### Recovery Procedures
In case of lockout from either password manager:
1. Printed recovery keys are stored in the [[Church Safe]]
2. Contact the [[Church Administrator]] for access

### Password Requirements
- Password manager master passwords must be:
  - Highly secure
  - Unique
  - Regularly updated
  - Never shared

## Related Documentation
- [[Security Protocols]]
- [[Administrative Access]]
- [[Emergency Procedures]]

## Templates
- [[Password Reset Procedure]]
- [[Account Access Request]]
- [[Security Incident Report]]