---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: resource
tags: [para/resources, templater, automation, daily-notes, setup]
related: [Templates, Daily Notes TOC]
---

# Templater Auto-Processing Setup Guide

## Problem
When using Obsidian's built-in daily notes plugin, the template is created but Templater placeholders (like `2025-06-19`) are not automatically processed, requiring manual execution of "Templater: replace templates in current file" each time.

## Solutions

### 🎯 **Solution 1: Quick Fix - Clickable Link (ADDED)**

I've added a clickable link to all your daily note templates:

```markdown
> **🔄 Template not processed?** Click here: [Process Template](obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater)
```

**How it works:**
- Click the link when template placeholders aren't processed
- Automatically runs the Templater command
- Processes all `undefined` placeholders in the current file

### ⚙️ **Solution 2: Configure Templater Auto-Processing**

#### Step 1: Enable Templater Auto-Processing
1. **Go to Settings** → **Templater**
2. **Enable "Trigger Templater on new file creation"**
3. **Set "Timeout"** to 5000ms (5 seconds) for complex templates

#### Step 2: Configure Daily Notes Integration
1. **Go to Settings** → **Daily notes**
2. **Set Template file path** to your preferred template:
   - `cruca-documentation/Templates/Enhanced Church Daily Template.md` (Recommended)
   - `cruca-documentation/Templates/Daily Note Template.md` (Simple)

#### Step 3: Enable Folder Templates (Optional)
1. **In Templater settings** → **Enable "Folder Templates"**
2. **Add folder template:**
   - **Folder**: `cruca-documentation/0-Journals-Daily Notes`
   - **Template**: `cruca-documentation/Templates/Enhanced Church Daily Template.md`

### 🔧 **Solution 3: Use Templater's Daily Notes Instead**

#### Option A: Templater Hotkey
1. **Go to Settings** → **Hotkeys**
2. **Search for "Templater: Create new note from template"**
3. **Assign a hotkey** (e.g., `Ctrl+Shift+D`)
4. **Use this instead** of the built-in daily notes button

#### Option B: Templater Command
1. **Open Command Palette** (`Ctrl+P`)
2. **Type "Templater: Create new note from template"**
3. **Select your daily note template**
4. **Template will be fully processed**

### 🚀 **Solution 4: Advanced URI Integration**

For power users, you can create custom buttons or shortcuts:

#### Desktop Shortcut
Create a desktop shortcut with this URL:
```
obsidian://advanced-uri?vault=cruca-docs&daily=true&template=cruca-documentation/Templates/Enhanced%20Church%20Daily%20Template.md
```

#### Browser Bookmark
Save this as a bookmark for quick daily note creation:
```
obsidian://advanced-uri?vault=cruca-docs&daily=true&template=cruca-documentation/Templates/Enhanced%20Church%20Daily%20Template.md&execute=true
```

## Recommended Workflow

### 🌟 **Best Practice:**
1. **Use the built-in daily notes** as you normally do
2. **If template isn't processed**, click the "Process Template" link I added
3. **Consider switching to Templater hotkey** for fully automated experience

### ⚙️ **Settings to Check:**

#### Templater Settings:
- ✅ **Trigger Templater on new file creation**: ON
- ✅ **Timeout**: 5000ms
- ✅ **Enable Folder Templates**: ON (optional)
- ✅ **Template folder path**: `cruca-documentation/Templates`

#### Daily Notes Settings:
- ✅ **Date format**: `YYYY-MM-DD`
- ✅ **New file location**: `cruca-documentation/0-Journals-Daily Notes`
- ✅ **Template file location**: `cruca-documentation/Templates/Enhanced Church Daily Template.md`

## Troubleshooting

### Issue: Template still not processing automatically
**Solutions:**
1. **Increase timeout** in Templater settings to 10000ms
2. **Disable and re-enable** Templater plugin
3. **Use the clickable link** I added to templates
4. **Switch to Templater hotkey** instead of daily notes plugin

### Issue: Advanced URI link not working
**Solutions:**
1. **Install Advanced URI plugin** if not already installed
2. **Check vault name** matches exactly (case-sensitive)
3. **Use the manual command** instead: `Ctrl+P` → "Templater: replace templates in current file"

### Issue: Task rollover not working
**Solutions:**
1. **Ensure file path** in template matches your daily notes folder
2. **Check file naming** follows `YYYY-MM-DD.md` format
3. **Click "Process Template" link** to run the script

## Alternative: QuickAdd Plugin

For the most automated experience, consider installing **QuickAdd plugin**:

1. **Install QuickAdd** from Community Plugins
2. **Create a macro** that:
   - Creates daily note with current date
   - Applies template
   - Processes Templater automatically
3. **Assign hotkey** to the macro

## Templates Updated

I've added the "Process Template" link to these templates:
- ✅ **Enhanced Church Daily Template** (Recommended)
- ✅ **EnhancedDaily** 
- ✅ **Daily Note Template**

## Related Resources
- [[Templates/Enhanced Church Daily Template]] - Main template with auto-processing link
- [[Daily Notes Template Fix Summary]] - Overview of all template improvements
- [[Templates TOC]] - All available templates
- [Templater Documentation](https://silentvoid13.github.io/Templater/)
- [Advanced URI Plugin](https://github.com/Vinzent03/obsidian-advanced-uri)
