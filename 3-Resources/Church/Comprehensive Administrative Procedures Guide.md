---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: resource
source: Comprehensive Church Administration Guide
tags:
  - para/resources
  - admin
  - procedures
  - church
  - cruca
  - multi-church
  - administration
  - finance
  - hall-hire
  - safe-church
  - weekly
  - monthly
  - annual
related:
  - Church Administration
  - Administration Overview
  - Church TOC
  - Hall Hire Procedures
  - Safe Church
area: Church-Administration
scope: Multi-Church Administration (3 Churches)
churches: [Caboolture, Beachmere, Upper Caboolture]
---

# Comprehensive Multi-Church Administration Procedures Guide
*Managing 3 Uniting Churches End-to-End*

## Overview
This guide provides comprehensive procedures for independently managing three Uniting Church congregations (Caboolture, Beachmere, Upper Caboolture) as an administrator. It includes weekly, fortnightly, monthly, and annual procedures with smart reminders based on time of year and events.

## Quick Navigation
- [[#Weekly Procedures]]
- [[#Fortnightly Procedures]]
- [[#Monthly Procedures]]
- [[#Annual Procedures]]
- [[#Event-Based Reminders]]
- [[#Emergency Procedures]]

---

# Weekly Procedures

## Monday Tasks
### Bible Study & Prayer Group Support
- **7:30am**: Monitor Bible Study attendance (Caboolture)
- **7:30am**: Ensure Prayer Group has access and resources
- Check heating/cooling systems before groups arrive
- Verify security codes are working

### Administrative Tasks
- [ ] Check and respond to weekend emails
- [ ] Review hall hire requests from weekend
- [ ] Update church calendar with new bookings
- [ ] Process any urgent finance matters

## Tuesday Tasks
### Healing Rooms Support
- **10am-2pm**: Ensure Healing Rooms has access (Caboolture)
- Check supplies and facilities
- Monitor any special requirements

### Finance & Banking
- [ ] Download and review bank statements
- [ ] Process OSKO payments and donations
- [ ] Update donation tracking spreadsheet
- [ ] Identify and follow up unidentified payments

## Wednesday Tasks
### Craft Groups & Activities
- **Friendly Corner** (2nd & 4th Wed, 9:30am)
- **Caboolture Craft Group** (1st & 3rd Wed, 9:30am)
- **Beachmere Craft Group** (2nd & 4th Wed, 1:30pm)
- **5th Wednesday**: Cafe arrangements for Caboolture Craft Group

### Administrative Tasks
- [ ] Mid-week facility checks
- [ ] Process hall hire applications
- [ ] Update event calendars
- [ ] Coordinate with ministry teams

## Thursday Tasks
### Know Your Bible & Groups
- **KYB** (1st & 3rd Thu, 1pm) - Caboolture
- **Avachat** (Every Thu, 8am) - Coffee Club Central Lakes
- **Upper Caboolture Craft Group** (2nd & 4th Thu, 1:30pm)
- **Palm Lakes Care** (1st Thu monthly, 10:30am) - Beachmere

### Weekly Finance Review
- [ ] Reconcile weekly donations
- [ ] Update financial records
- [ ] Prepare banking for Friday
- [ ] Review outstanding invoices

## Friday Tasks
### Boys Brigade Support
- **6pm-10pm**: Boys Brigade (School terms only)
- Ensure facilities are prepared
- Check security and safety requirements

### Cafe Church Beachmere
- **Last Friday monthly, 10am**: Cafe Church preparation
- Coordinate with ministry team
- Arrange catering if required

### End of Week Tasks
- [ ] Complete weekly financial reconciliation
- [ ] Backup important data
- [ ] Prepare weekend service requirements
- [ ] Update weekly reports

## Saturday Tasks
### Church1 Congregation Support
- **4:30pm**: Support other congregation (Contact: Bernice 0425 441 969)
- Coordinate facility sharing
- Ensure access and security

### Weekend Preparation
- [ ] Final checks for Sunday services
- [ ] Ensure all facilities are ready
- [ ] Check sound/technical equipment
- [ ] Prepare offering counting materials

## Sunday Tasks
### Service Support
- **Sunnymeade Service** (3rd Sunday monthly)
- Support all three congregations
- Coordinate between services

### Post-Service Tasks
- [ ] Secure facilities
- [ ] Count and bank offerings
- [ ] Update attendance records
- [ ] Note any maintenance issues

---

# Smart Event Reminders

## Current Week Events
```dataview
TABLE WITHOUT ID
  file.link as "Event",
  date as "Date",
  location as "Location",
  tags as "Type"
FROM "Events&Meeting Dates" OR "2-Areas/0-Monthly-Tasks&Reports"
WHERE date >= date(today) AND date <= date(today) + dur(7 days)
SORT date ASC
```

## This Month's Special Events
```dataview
TABLE WITHOUT ID
  file.link as "Event",
  date as "Date",
  location as "Location"
FROM "Events&Meeting Dates"
WHERE date >= date(today) AND date <= date(today) + dur(30 days)
SORT date ASC
```

## Recurring Weekly Events Requiring Admin Support
```dataview
TABLE WITHOUT ID
  file.link as "Activity",
  tags as "Day/Time",
  location as "Location"
FROM "Events&Meeting Dates"
WHERE contains(tags, "recurring") OR contains(tags, "weekly")
SORT file.name ASC
```

---

# Hall Hire Management

## Hall Hire Quick Checklist
- [ ] Check [[Events&Meeting Dates/Event List|Event List]] for conflicts
- [ ] Verify church calendar availability
- [ ] Confirm $19/hour fee (minimum $38)
- [ ] Verify client has $20m insurance
- [ ] Send Non-exclusive License Agreement form
- [ ] Process invoice payment
- [ ] Assign security code and physical key
- [ ] Update key register
- [ ] Confirm booking via email
- [ ] Send safety information and induction checklist

## Hall Hire Dataview Tracking
```dataview
TABLE WITHOUT ID
  file.link as "Booking",
  date as "Date",
  status as "Status",
  payment as "Payment Status"
FROM "Administration/Hall Hire" OR "2-Areas"
WHERE contains(tags, "hall-hire") OR contains(tags, "booking")
SORT date ASC
```

---

# Finance & Banking Procedures

## Daily Banking Tasks
### Accessing Bank Statements (BBO)
1. Log in to Business Banking Online (BBO) portal
2. Navigate to Accounts section
	1. Select appropriate account (BSB: 334-040, Account: 000 0553 861 353)
3. Choose date range for statement
4. Download statement in desired format

### OSKO Payment Processing
1. Download bank statement with OSKO payment details
2. Match donations to donor records
3. Update donation tracking spreadsheet
4. Note unidentified donations for follow-up
5. Contact bank for additional information if necessary
6. Announce unidentified donations during service if needed

## Weekly Finance Tasks
- [ ] Reconcile all donations and payments
- [ ] Update financial tracking systems
- [ ] Process hall hire payments
- [ ] Review and pay outstanding invoices
- [ ] Backup financial data

## Monthly Finance Tasks
- [ ] Generate monthly financial reports
- [ ] Reconcile all bank accounts
- [ ] Process monthly bills and utilities
- [ ] Update budget vs actual analysis
- [ ] Prepare council meeting financial reports

---

# Safe Church Compliance

## Ongoing Safe Church Tasks
- [ ] Update worker and volunteer records
- [ ] Maintain training completion records
- [ ] Review and update safety plans
- [ ] Monitor Blue Card compliance
- [ ] Update ministry area documentation

## Safe Church Dataview Tracking
```dataview
TABLE WITHOUT ID
  file.link as "Document",
  file.mtime as "Last Updated",
  tags as "Type"
FROM "Administration/Safe Church" OR "2-Areas/Safe Church"
WHERE contains(tags, "safe-church") OR contains(file.folder, "Safe Church")
SORT file.mtime DESC
```

---

# Fortnightly Procedures

## Week 1 Tasks (Odd Weeks)
### Facility Management
- [ ] Deep clean of main facilities
- [ ] Equipment maintenance checks
- [ ] Review and update duty rosters
- [ ] Test security and alarm systems

### Administrative Review
- [ ] Review previous fortnight's activities
- [ ] Update administrative documentation
- [ ] Process fortnightly payroll (if applicable)
- [ ] Coordinate with ministry teams

## Week 2 Tasks (Even Weeks)
### Financial Management
- [ ] Comprehensive financial reconciliation
- [ ] Update insurance records and claims
- [ ] Review safety and security procedures
- [ ] Process superannuation payments

### Compliance & Reporting
- [ ] Review Synod requirements and updates
- [ ] Update compliance documentation
- [ ] Coordinate with external contractors
- [ ] Plan upcoming events and activities

---

# Monthly Procedures

## First Week of Month
### Monthly Setup
- [ ] Create monthly task folder: `2-Areas/0-Monthly-Tasks&Reports/[Month]`
- [ ] Review previous month's reports and outcomes
- [ ] Update annual event calendar
- [ ] Schedule monthly council and committee meetings

### Finance Initialization
- [ ] Generate monthly financial reports
- [ ] Process monthly bills and utilities (AGL, etc.)
- [ ] Review hall hire income and bookings
- [ ] Update donation and offering records

## Second Week of Month
### Compliance & Safety Review
- [ ] Safe Church compliance review and updates
- [ ] Update worker/volunteer records in Protecht
- [ ] Review insurance requirements and coverage
- [ ] Check fire safety equipment and procedures
- [ ] Update Blue Card organization portal

### Communication & Outreach
- [ ] Prepare monthly bulletin content
- [ ] Update church website with current events
- [ ] Send monthly newsletters to congregations
- [ ] Coordinate with ministry teams for upcoming events

## Third Week of Month
### Facilities & Maintenance
- [ ] Conduct comprehensive facility inspections
- [ ] Schedule and coordinate maintenance work
- [ ] Review cleaning schedules and standards
- [ ] Update equipment registers and warranties
- [ ] Check sound desk and technical equipment

### Event Planning
- [ ] Plan and coordinate next month's events
- [ ] Schedule special services and celebrations
- [ ] Review staffing and volunteer requirements
- [ ] Update event calendars and bookings

## Fourth Week of Month
### Reporting & Review
- [ ] Complete monthly administrator report for council
- [ ] Prepare council meeting materials and agenda items
- [ ] Review monthly KPIs and performance metrics
- [ ] Plan improvements and changes for next month

### Month-End Finance
- [ ] Complete comprehensive monthly reconciliation
- [ ] Generate financial statements for council
- [ ] Process month-end adjustments and corrections
- [ ] Backup all financial data and records

---

# Annual Procedures

## Quarterly Tasks (Every 3 Months)

### Q1 (January-March): Planning & Setup
- [ ] Annual budget review and strategic planning
- [ ] Insurance policy renewals and reviews
- [ ] Annual compliance audits and assessments
- [ ] Strategic planning sessions with leadership

### Q2 (April-June): Mid-Year Review
- [ ] Mid-year financial review and budget adjustments
- [ ] Staff and volunteer performance reviews
- [ ] Facility maintenance planning and scheduling
- [ ] Program effectiveness review and improvements

### Q3 ( #July - #September): Preparation Phase
- [ ] Annual meeting preparation and planning
- [ ] Budget preparation for following year
- [ ] Annual report compilation and review
- [ ] Election preparation (if applicable)

### Q4 (October-December): Completion & Celebration
- [ ] Annual financial statements preparation
- [ ] Tax and compliance submissions
- [ ] Annual meeting execution and follow-up
- [ ] Year-end celebrations and volunteer recognition

## Annual Events Calendar

### Fixed Annual Events
```dataview
TABLE WITHOUT ID
  event as "Event",
  month as "Month",
  description as "Description"
FROM "Events&Meeting Dates"
WHERE contains(tags, "annual") OR contains(tags, "yearly")
SORT month ASC
```

### Key Annual Dates
- **Mother's Day Morning Tea**: May 3rd (or first Sunday in May)
- **Election Day Market**: May (date varies with election schedule)
- **Annual Church Meeting**: Usually October
- **Christmas Services**: December (multiple services)
- **Easter Services**: March/April (date varies annually)

### Recurring Annual Administrative Tasks
- [ ] **January**: Annual insurance review and renewals
- [ ] **February**: Safe Church training updates and compliance
- [ ] **March**: Financial audit preparation and Easter planning
- [ ] **April**: Mid-year planning and budget review
- [ ] **May**: Mother's Day and Election Day events
- [ ] **June**: Mid-year reports and assessments
- [ ] **July**: Annual meeting preparation begins
- [ ] **August**: Budget planning for next year
- [ ] **September**: Annual report compilation
- [ ] **October**: Annual meeting and elections
- [ ] **November**: Christmas planning and preparation
- [ ] **December**: Year-end celebrations and reporting

---

# Emergency Procedures

## Emergency Contacts
- **Church Office**: [Insert number]
- **Minister**: [Insert number]
- **Church Council Chair**: [Insert number]
- **Property Emergency**: [Insert number]
- **Security Company**: [Insert number]

## Emergency Response Checklist
- [ ] Assess situation and ensure personal safety
- [ ] Contact emergency services if required (000)
- [ ] Notify church leadership immediately
- [ ] Document incident thoroughly
- [ ] Follow up with insurance if applicable
- [ ] Review and update procedures if needed

## Incident Reporting
- [ ] Complete incident report form
- [ ] Notify relevant authorities
- [ ] Update safety procedures
- [ ] Communicate with affected parties
- [ ] Review insurance implications

---

# Smart Dataview Dashboards

## Upcoming Tasks This Week
```dataview
TASK
FROM "2-Areas" OR "1-Projects"
WHERE !completed AND due <= date(today) + dur(7 days)
SORT due ASC
```

## Monthly Reports Due
```dataview
TABLE WITHOUT ID
  file.link as "Report",
  due as "Due Date",
  status as "Status"
FROM "2-Areas/0-Monthly-Tasks&Reports"
WHERE contains(tags, "report") AND due >= date(today)
SORT due ASC
```

## Hall Hire Bookings This Month
```dataview
TABLE WITHOUT ID
  file.link as "Booking",
  date as "Date",
  client as "Client",
  status as "Status"
FROM "Administration/Hall Hire"
WHERE date >= date(today) AND date <= date(today) + dur(30 days)
SORT date ASC
```

## Finance Tasks Requiring Attention
```dataview
TABLE WITHOUT ID
  file.link as "Task",
  priority as "Priority",
  due as "Due Date"
FROM "2-Areas/Finance Billing" OR "Finance"
WHERE contains(tags, "urgent") OR contains(tags, "overdue")
SORT priority DESC, due ASC
```

## Safe Church Compliance Status
```dataview
TABLE WITHOUT ID
  file.link as "Item",
  status as "Status",
  review_date as "Next Review"
FROM "Administration/Safe Church" OR "2-Areas/Safe Church"
WHERE review_date <= date(today) + dur(30 days)
SORT review_date ASC
```

---

# Related Resources
- [[Hall Hire Procedures]]
- [[cruca-documentation/Administration/Admin-Documentation-Guides/Bank Account and Finance Guides]]
- [[Email Server Settings]]
- [[CRUCA-Church-Vault/3-Resources/Church/Membership Definitions]]
- [[Administration Overview]]
- [[Church TOC]]
- [[Resources TOC]]
- [[Safe Church Implementation]]
- [[Event List]]
- [[Templates/Church Report Template]]
