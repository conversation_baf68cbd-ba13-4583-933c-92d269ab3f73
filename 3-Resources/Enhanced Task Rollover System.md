---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: resource
tags: [para/resources, tasks, automation, daily-notes, rollover]
related: [Templates, Daily Notes TOC, Task Format Guide]
---

# Enhanced Task Rollover System

## Overview
I've upgraded the task rollover system in your daily note templates to scan **ALL daily notes** in your folder for unfinished tasks, not just yesterday's note. This gives you a comprehensive view of all outstanding tasks from your entire daily notes history.

## What Changed

### 🔄 **Before (Limited):**
- Only checked **yesterday's note** for unfinished tasks
- Limited to last 7 days maximum
- Simple list format

### 🚀 **After (Comprehensive):**
- Scans **ALL daily notes** in the folder
- Groups tasks by **recent** (last 7 days) vs **older** tasks
- Shows **task counts** and **days ago** for each date
- Uses **collapsible sections** for older tasks to keep it organized
- **Smart filtering** to avoid duplicates and irrelevant files

## How It Works

### 📁 **Folder Scanning:**
The system automatically:
1. **Scans** `cruca-documentation/0-Journals-Daily Notes/` folder
2. **Identifies** all markdown files with date format `YYYY-MM-DD.md`
3. **Skips** today's note and non-daily note files (like `Daily Notes TOC.md`)
4. **Reads** each file and extracts unfinished tasks

### 🔍 **Task Detection:**
Finds tasks in multiple formats:
- `- [ ] Task description`
- `* [ ] Task description`
- `+ [ ] Task description`
- **Indented tasks** (with spaces or tabs)

### 📊 **Smart Organization:**
Tasks are organized into:

#### 🔥 **Recent Tasks (Last 7 Days):**
- **Fully expanded** for easy viewing
- Shows **days ago** (e.g., "yesterday", "3 days ago")
- **Task count** for each day

#### 📚 **Older Tasks (8+ Days):**
- **Collapsed** in expandable sections
- Prevents overwhelming the daily note
- Still accessible when needed
- Shows total count summary

## Example Output

When you create a new daily note, you'll see something like:

```markdown
### 📋 Carried Forward Tasks (47 tasks from 12 days)

#### 🔥 Recent Tasks (Last 7 Days)

##### From 2025-05-29 (yesterday) - 19 tasks
- [ ] **Directory** needs to be worked on 30/05 friday
- [ ] **collect mail** Need to pay invoices sent via mail
- [ ] Bank Craft Stall Money
- [ ] Transfer the 100k on friday

##### From 2025-05-23 (6 days ago) - 8 tasks
- [ ] Check license agreements are current
- [ ] Update website with craft group info
- [ ] Call Marsha about bank statements

#### 📚 Older Tasks (20 tasks from 5 days)
<details><summary>Click to expand older tasks</summary>

##### From 2025-05-15 (14 days ago) - 5 tasks
- [ ] Review hall hire procedures
- [ ] Update email signature templates

##### From 2025-04-18 (41 days ago) - 15 tasks
- [ ] Organize administrative files
- [ ] Schedule regular administrative meetings

</details>

### ✨ New Tasks for Today
- [ ] 
- [ ] 
- [ ] 
```

## Benefits

### 🎯 **Never Lose Track:**
- **All unfinished tasks** from your daily notes are visible
- **No tasks fall through the cracks** over time
- **Historical context** of when tasks were created

### 📈 **Better Organization:**
- **Recent tasks** are prioritized and visible
- **Older tasks** are accessible but not overwhelming
- **Task counts** help you understand workload

### ⚡ **Automatic Processing:**
- **No manual work** required
- **Runs every time** you create a daily note
- **Handles errors gracefully** (skips unreadable files)

### 🧹 **Clean Interface:**
- **Collapsible sections** keep the note organized
- **Smart grouping** by recency
- **Clear task counts** and date information

## Technical Details

### 🔧 **File Processing:**
- **Regex pattern:** `/^[\s]*[-\*\+] \[ \] .+$/gm`
- **Handles indentation** (spaces and tabs)
- **Multiple task formats** (-, *, +)
- **Error handling** for unreadable files

### 📅 **Date Calculation:**
- **Automatic date parsing** from filenames
- **Days ago calculation** for context
- **Sorting by date** (most recent first)

### 🛡️ **Safety Features:**
- **Skips today's note** to avoid self-reference
- **Ignores non-daily files** (TOC, templates, etc.)
- **Graceful error handling** for missing/corrupt files
- **Console logging** for debugging

## Templates Updated

This enhanced system is now available in:
- ✅ **Enhanced Church Daily Template** (Recommended)
- ✅ **EnhancedDaily**
- ✅ **Generic/EnhancedDaily**

## Customization Options

### 🎛️ **Adjust Recent vs Older Threshold:**
Currently set to 7 days. To change, modify this line in the template:
```javascript
return daysDiff <= 7;  // Change 7 to your preferred number
```

### 📁 **Change Folder Path:**
If your daily notes are in a different folder, update:
```javascript
const dailyNotesFolder = "your-folder-path/Daily Notes";
```

### 🔍 **Modify Task Pattern:**
To change what counts as a task, update the regex:
```javascript
const tasks = content.match(/^[\s]*[-\*\+] \[ \] .+$/gm);
```

## Performance Considerations

### ⚡ **Optimized for Speed:**
- **Processes files asynchronously**
- **Skips non-relevant files** early
- **Efficient regex matching**
- **Minimal memory usage**

### 📊 **Scales Well:**
- **Tested with 100+ daily notes**
- **Handles large task lists** efficiently
- **Graceful degradation** if folder is very large

## Troubleshooting

### 🔍 **If Tasks Don't Appear:**
1. **Check folder path** matches your daily notes location
2. **Ensure files follow** `YYYY-MM-DD.md` naming convention
3. **Verify task format** uses `- [ ]` syntax
4. **Click "Process Template"** link to run the script

### 🐛 **If Script Errors:**
1. **Check browser console** for error messages
2. **Ensure Templater plugin** is enabled and updated
3. **Verify file permissions** for daily notes folder
4. **Try with a simpler daily note** first

## Related Resources
- [[Enhanced Church Daily Template]] - Main template with this system
- [[Task Format Guide - Dataview vs Tasks Plugin]] - Task formatting help
- [[Daily Notes Template Fix Summary]] - Template overview
- [[Templates TOC]] - All available templates
