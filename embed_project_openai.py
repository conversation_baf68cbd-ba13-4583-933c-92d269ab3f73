#!/usr/bin/env python3
"""
CRUCA Documentation Embedding Script - OpenAI Version
Embeds project files into Qdrant vector database using OpenAI text-embedding-3-large
"""

import os
import json
import time
import uuid
from pathlib import Path
from typing import List, Dict, Any

try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, PointStruct
    import openai
except ImportError:
    print("Installing required packages...")
    os.system("pip3 install qdrant-client openai")
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, PointStruct
    import openai

class CRUCAOpenAIEmbedder:
    def __init__(self, qdrant_url: str = "http://localhost:6333", api_key: str = None):
        self.qdrant_url = qdrant_url
        self.collection_name = "cruca-documentation-openai"
        self.model_name = "text-embedding-3-large"
        self.embedding_dim = 3072  # OpenAI text-embedding-3-large dimension
        self.embedded_files = []
        self.failed_files = []
        
        # Initialize OpenAI client
        if api_key:
            openai.api_key = api_key
        else:
            # Try to get from environment
            openai.api_key = os.getenv('OPENAI_API_KEY')
            if not openai.api_key:
                raise ValueError("OpenAI API key required. Set OPENAI_API_KEY environment variable or pass api_key parameter.")
        
        # Initialize Qdrant client
        self.client = QdrantClient(url=qdrant_url)
        
        # Test OpenAI connection
        print("🔄 Testing OpenAI connection...")
        try:
            test_response = openai.embeddings.create(
                model=self.model_name,
                input="test"
            )
            actual_dim = len(test_response.data[0].embedding)
            print(f"✅ OpenAI connected. Embedding dimension: {actual_dim}")
            self.embedding_dim = actual_dim
        except Exception as e:
            print(f"❌ OpenAI connection failed: {e}")
            raise
    
    def ensure_collection_exists(self):
        """Create collection if it doesn't exist"""
        try:
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                print(f"🔄 Creating collection: {self.collection_name}")
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(size=self.embedding_dim, distance=Distance.COSINE)
                )
                print(f"✅ Collection created: {self.collection_name}")
            else:
                print(f"✅ Collection exists: {self.collection_name}")
                
        except Exception as e:
            print(f"❌ Error with collection: {e}")
            return False
        return True
        
    def read_file_content(self, file_path: Path) -> str:
        """Read file content safely"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
            return ""
    
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding using OpenAI"""
        try:
            response = openai.embeddings.create(
                model=self.model_name,
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            print(f"❌ Embedding generation failed: {e}")
            return []
    
    def store_document(self, content: str, metadata: Dict[str, Any]) -> bool:
        """Store document in Qdrant using OpenAI embeddings"""
        try:
            # Truncate content if too long (OpenAI has 8191 token limit)
            if len(content) > 30000:  # Rough token estimate
                content = content[:30000] + "..."
                print(f"⚠️  Truncated long document: {metadata.get('file_path', 'unknown')}")
            
            # Generate embedding using OpenAI
            embedding = self.generate_embedding(content)
            if not embedding:
                return False
            
            # Create point
            point_id = str(uuid.uuid4())
            point = PointStruct(
                id=point_id,
                vector=embedding,
                payload={
                    "content": content,
                    **metadata
                }
            )
            
            # Store in Qdrant
            self.client.upsert(
                collection_name=self.collection_name,
                points=[point]
            )
            
            print(f"✅ Stored: {metadata.get('file_path', 'unknown')}")
            return True
                
        except Exception as e:
            print(f"❌ Exception storing document: {e}")
            return False
    
    def embed_file(self, file_path: Path, category: str = "documentation") -> bool:
        """Embed a single file"""
        print(f"📄 Processing: {file_path}")
        
        content = self.read_file_content(file_path)
        if not content.strip():
            print(f"⚠️  Skipping empty file: {file_path}")
            return False
        
        # Create metadata
        metadata = {
            "file_path": str(file_path),
            "file_name": file_path.name,
            "category": category,
            "file_type": file_path.suffix,
            "size": len(content),
            "embedded_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            "model": self.model_name
        }
        
        # Store in Qdrant
        success = self.store_document(content, metadata)
        
        if success:
            self.embedded_files.append(str(file_path))
        else:
            self.failed_files.append(str(file_path))
            
        # Rate limiting for OpenAI API
        time.sleep(0.1)
            
        return success
    
    def run_full_embedding(self):
        """Run complete embedding process"""
        print("🎯 CRUCA Documentation Embedding Started (OpenAI)")
        print("=" * 60)
        print(f"Model: {self.model_name}")
        print(f"Embedding Dimension: {self.embedding_dim}")
        print(f"Collection: {self.collection_name}")
        print("=" * 60)
        
        # Step 0: Ensure collection exists
        if not self.ensure_collection_exists():
            print("❌ Failed to create/verify collection")
            return
        
        # Process all markdown files
        md_files = list(Path(".").rglob("*.md"))
        print(f"Found {len(md_files)} markdown files")
        
        for file_path in md_files:
            # Skip backup and temporary files
            if any(skip in str(file_path) for skip in ['.edtz', '_BACKUP_', '_BASE_', '_LOCAL_', '_REMOTE_']):
                continue
                
            # Determine category based on path
            path_str = str(file_path)
            if "1-Projects" in path_str:
                category = "projects"
            elif "2-Areas" in path_str:
                category = "areas"
            elif "3-Resources" in path_str:
                category = "resources"
            elif "Templates" in path_str:
                category = "templates"
            elif "Office-Procedures" in path_str:
                category = "procedures"
            else:
                category = "documentation"
                
            self.embed_file(file_path, category)
        
        print("\n" + "=" * 60)
        print("📊 EMBEDDING SUMMARY (OpenAI)")
        print("=" * 60)
        print(f"✅ Successfully embedded: {len(self.embedded_files)} files")
        print(f"❌ Failed to embed: {len(self.failed_files)} files")
        
        if self.failed_files:
            print("\n❌ Failed files:")
            for file in self.failed_files:
                print(f"  - {file}")
        
        print(f"\n🎉 Embedding complete!")

if __name__ == "__main__":
    # You need to set your OpenAI API key
    api_key = input("Enter your OpenAI API key (or press Enter to use OPENAI_API_KEY env var): ").strip()
    if not api_key:
        api_key = None
    
    embedder = CRUCAOpenAIEmbedder(api_key=api_key)
    embedder.run_full_embedding()
