---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: dashboard
aliases: [Home, Dashboard]
tags: [dashboard, home]
---

# Caboolture Region Uniting Church Australia

Welcome to the CRUCA documentation vault. This vault contains all administrative documentation, reports, and resources related to Caboolture Region Uniting Church Australia.

## Quick Navigation
- [[1-Projects/Projects TOC|Projects]]
- [[2-Areas/Areas TOC|Areas]]
- [[3-Resources/Resources TOC|Resources]]
- [[4-Archive/Archive TOC|Archive]]
- [[Templates/Templates TOC|Templates]]
- [[Daily Notes TOC|Daily Notes]]
- [[Todoist/Todoist TOC|Todoist Tasks]]

## Current Focus
```dataview
TABLE
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC
LIMIT 5
```

## Recent Monthly Reports
```dataview
LIST
FROM "Monthly-Reports&Tasks"
SORT file.mtime DESC
LIMIT 5
```

## Administration
```dataview
LIST
FROM "Administration"
LIMIT 5
```

## Hall Hire
```dataview
LIST
FROM "Administration/Hall Hire"
SORT file.mtime DESC
LIMIT 5
```

## Safe Church
```dataview
LIST
FROM "Administration/Safe Church"
SORT file.mtime DESC
LIMIT 5
```

## Todoist Tasks
```dataview
LIST
FROM "Todoist"
SORT file.mtime DESC
LIMIT 3
```

## Recent Daily Notes
```dataview
TABLE
  file.mtime as "Last Modified"
FROM "Daily Notes"
WHERE file.name != "Daily Notes TOC"
SORT file.mtime DESC
LIMIT 5
```

## Recent Notes
```dataview
TABLE
  file.mtime as "Last Modified"
FROM "."
WHERE file.name != "Home" AND !contains(file.folder, "Daily Notes")
SORT file.mtime DESC
LIMIT 10
```
