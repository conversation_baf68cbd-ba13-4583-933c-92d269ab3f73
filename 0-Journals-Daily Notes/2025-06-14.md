---
creation_date: 2025-06-19 15:50
modification_date: Thursday 19th June 2025 15:50:56
type: daily
date: 2025-06-19
day_of_week: Thursday
week: 2025-W25
month: 2025-06
tags: [daily, 2025-06, cruca]
mood: ""
energy_level: ""
weather: ""
location: ""
---

# 2025-06-19 - Thursday

<< [[2025-06-18]] | [[2025-06-20]] >>

> **🔄 Template not processed?** Click here: [Process Template](obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater)

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
- 

## Tasks for Today
#todo #tasks #outstanding
### ✨ Tasks for Today

- [ ] 
- [ ] 
- [ ] 

## Church Matters
<!-- Church-related activities, meetings, pastoral care, services -->
- 

## Hall Hire
<!-- Hall bookings, inquiries, maintenance, key management -->
- 

## Administration
<!-- Administrative tasks, correspondence, documentation, reports -->
- 

## Finance & Banking
<!-- Banking, donations, payments, reconciliation, invoices -->
- 

## Safe Church
<!-- Compliance, training, documentation, Blue Card updates -->
- 

## Communication
<!-- Emails, phone calls, website updates, social media -->
- 

## Facilities & Maintenance
<!-- Building maintenance, equipment, cleaning, security -->
- 

## Follow-ups
<!-- Items requiring follow-up action with specific people/organizations -->
- [ ] 
- [ ] 

## Journal
<!-- How was your day? What happened? What did you learn? -->
- 

## Notes
<!-- Any other notes or information -->
- 

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-06-19)
SORT time ASC
```

## Today's Tasks Overview
```tasks
not done
due on 2025-06-19
```

## Tasks from This Note (All)
```dataview
TASK
FROM "cruca-documentation-git/Templates/Routine Administrative Procedures.md"
WHERE !completed
```

## Tasks from This Note (With Due Dates)
```dataview
TASK
FROM "cruca-documentation-git/Templates/Routine Administrative Procedures.md"
WHERE !completed AND due
SORT due ASC
```

## This Week's Tasks (Dataview)
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-06-15) AND due <= date(2025-06-21)
SORT due ASC
```

## This Week's Tasks (Tasks Plugin)
```tasks
not done
due after 2025-06-14
due before 2025-06-22
```

## This Month's Tasks (Dataview)
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-06-01) AND due <= date(2025-06-31)
SORT due ASC
```

## This Month's Tasks (Tasks Plugin)
```tasks
not done
due after 2025-06-01
due before 2025-07-01
```

## This Quarter's Tasks

```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-04-01) AND due <= date(2025-06-30)
SORT due ASC
```

## Overdue Tasks
```tasks
not done
due before 2025-06-19
```

## Upcoming Tasks (Next 7 Days)
```tasks
not done
due after 2025-06-19
due before 2025-06-26
```

## All Incomplete Tasks (No Due Date Required)
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed
SORT file.name ASC
LIMIT 20
```

## Church Administration Tasks (With Due Dates)
```dataview
TASK
FROM "2-Areas" OR "1-Projects"
WHERE !completed AND due AND (contains(tags, "church") OR contains(tags, "admin") OR contains(tags, "hall-hire") OR contains(tags, "finance"))
SORT due ASC
LIMIT 10
```

## Church Administration Tasks (All)
```dataview
TASK
FROM "2-Areas" OR "1-Projects"
WHERE !completed AND (contains(tags, "church") OR contains(tags, "admin") OR contains(tags, "hall-hire") OR contains(tags, "finance"))
SORT file.name ASC
LIMIT 15
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Today's Events & Reminders
```dataview
TABLE WITHOUT ID
  file.link as "Event",
  time as "Time",
  location as "Location",
  description as "Description"
FROM "Events&Meeting Dates" OR "2-Areas/0-Monthly-Tasks&Reports"
WHERE date = date(2025-06-19)
SORT time ASC
```

## Weekly Recurring Events (Today: Thursday)
```dataview
TABLE WITHOUT ID
  file.link as "Activity",
  time as "Time",
  location as "Location",
  description as "Notes"
FROM "Events&Meeting Dates"
WHERE contains(tags, "recurring") AND contains(file.content, "Thursday")
```

## Create New Note
- [[2025-06-19 Meeting|Create New Meeting]]
- [[2025-06-19 Task|Create New Task]]
- [[2025-06-19 Hall Hire|Create Hall Hire Booking]]
- [[2025-06-19 Finance|Create Finance Note]]

## Related
- [[Daily Notes TOC]]
- [[2025-06|Monthly Overview]]
- [[2025-W25|Weekly Overview]]
- [[Comprehensive Administrative Procedures Guide]]
- [[Home]]
