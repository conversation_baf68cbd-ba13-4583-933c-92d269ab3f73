---
creation_date: 2025-09-09 00:40
modification_date: Tuesday 9th September 2025 00:40:37
type: daily
date: 2025-09-09
day_of_week: Tuesday
week: 2025-W37
month: 2025-09
tags: [daily, 2025-09, cruca]
mood: ""
energy_level: ""
weather: ""
location: ""
---

# 2025-09-09 - Tuesday

<< [[2025-09-08]] | [[2025-09-10]] >>

> **🔄 Template not processed?** Click here: [Process Template](obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater)

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
- 

## Tasks for Today
#todo #tasks #outstanding


### 📋 Carried Forward Tasks (54 unique tasks - duplicates removed)
#### 📚 Older Tasks (54 unique tasks - First Occurrence Only)
<details><summary>Click to expand older tasks</summary>

##### Originally from 2025-07-10 (61 days ago) - 1 unique tasks
- [ ] fix git repo for this vault. changed folders and now git history needs merging

##### Originally from 2025-07-03 (68 days ago) - 2 unique tasks
- [ ] Find unity water email & request contact update
- [ ] Find moreton bay council rates email & request contact update

##### Originally from 2025-06-28 (73 days ago) - 2 unique tasks

- [ ] Switch the keybinds in karibanar for ti-vim from command to fn (the current binding for control. Investigate further.) The problem resides with the keystroke sent from kabinar for the ^ keybind for ti-vim (start line) in the windows RDP sessions, the keys being sent are:
- [ ] ***Install the Stripe MCP server for church website assistance

##### Originally from 2025-06-26 (75 days ago) - 1 unique tasks

- [ ] Isabel Coming Today collecting mail i have and giving me the red/green dinner tickets

##### Originally from 2025-06-18 (83 days ago) - 1 unique tasks
- [ ] **** needs to be worked on 30/05 friday

##### Originally from 2025-06-13 (88 days ago) - 1 unique tasks
	- [ ] Elders & Pastoral Carers Gathering tuesday

##### Originally from 2025-05-29 (103 days ago) - 18 unique tasks
- [ ] **Directory** needs to be worked on 30/05 friday
- [ ] **collect mail** Need to pay invoices sent via mailfrom post office
- [ ] Change the 2025 CRUC Calendar - Events by X spreadsheet. :
	- [ ] swap the pacific combined dinner from 9 aug to 16th of august to accomodate the new bunnings saus Wendy has mentioned will be swapping
- [ ] order new business cards for information update 
- [ ] add the upper caboolture craft section to cruca.org website
- [ ] timesheet to fill for CRCC meetings
- [ ] check Koorong
- [ ] Transfer the 100k on friday as there is 100k limit on bank acc
- [ ] Check license agreements are current once a year (annual checks. over christmas holiday period)
- [ ] check communion cup stocks every month
- [ ] Email Telstra and contact to update details to get invoices sent better method
- [ ] contacts? ********** from 11/01 & **********
- [ ] Need to write up payment vouchers for the payments made today:
	- [ ] ![[cruca-documentation/2-Areas/Finance Billing/May/Pasted image **************.png]]
- [ ] investigate why telstra bill not paid and out of date:
	- [ ] call/contact telstra to update email adress. Ask peter if he has. see emails confirming he has <NAME_EMAIL>
- [ ] ![[cruca-documentation/3-Resources/attachments/Selfie 2025-05-30 at 01.33.22 1.png]]

##### Originally from 2025-05-16 (116 days ago) - 11 unique tasks
- [ ] #fayL:
	- [ ] **Garden Plants**
	- [ ] **Elders and Church Council Representatives**
	- [ ] **Are Your Skills Needed?**
- [ ] #jasonG:
	- [ ] Men’s Dinner
	- [ ] Elders & Pastoral Carers Gathering
- [ ] #fayM:
	- [ ] s
- [ ] #dawnG
	- [ ] Cafe church on friday 30th

##### Originally from 2025-05-15 (117 days ago) - 3 unique tasks
- [ ] ask #Ian about ncps account details for online.
- [ ] received john wiers personal info sheet for membership. Filed in the admin folder in desk cabinet for filing next thursday #Directory
- [ ] Reflect on #kirsty recommendation to discuss and complete the church directory alongside the annual reports

##### Originally from 2025-04-18 (144 days ago) - 6 unique tasks
- [ ] Investigate email errors with bulletin inserts
- [ ] Check graphics card issues on church computer
- [ ] Follow up with Ian about bulletin content
- [ ] Call Namecheap if quota exceeded error persists
- [ ] Arrange time to swap graphics card
- [ ] Confirm with Ian that bulletin inserts were received

##### Originally from 2025-04-16 (146 days ago) - 8 unique tasks
- [ ] Prepare administrator's report for April church council meeting
- [ ] Follow up on hall hire inquiry from Catherine Hicks
- [ ] Update website with new service times
- [ ] Check progress on Safe Church audit requirements
- [ ] Respond to emails from church council members
- [ ] Call Unity Water about changing bill delivery method to email
- [ ] Follow up with Peter Mortimer about account authorization
- [ ] Contact Synod about web domain for cabreguca.com.au

</details>

### ✨ New Tasks for Today


- [ ] 
- [ ] 
- [ ] 

## Church Matters
<!-- Church-related activities, meetings, pastoral care, services -->
- 

## Hall Hire
<!-- Hall bookings, inquiries, maintenance, key management -->
- 

## Administration
<!-- Administrative tasks, correspondence, documentation, reports -->
- 

## Finance & Banking
<!-- Banking, donations, payments, reconciliation, invoices -->
- 

## Safe Church
<!-- Compliance, training, documentation, Blue Card updates -->
- 

## Communication
<!-- Emails, phone calls, website updates, social media -->
- 

## Facilities & Maintenance
<!-- Building maintenance, equipment, cleaning, security -->
- 

## Follow-ups
<!-- Items requiring follow-up action with specific people/organizations -->
- [ ] 
- [ ] 

## Journal
<!-- How was your day? What happened? What did you learn? -->
- 

## Notes
<!-- Any other notes or information -->
- 

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-09-09)
SORT time ASC
```

## Today's Tasks Overview
```tasks
not done
due on 2025-09-09
```

## Tasks from This Note (All)
```dataview
TASK
FROM this.file
WHERE !completed
```

## Tasks from This Note (With Due Dates)
```dataview
TASK
FROM this.file
WHERE !completed AND due
SORT due ASC
```

## This Week's Tasks (Dataview)
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-09-08) AND due <= date(2025-09-14)
SORT due ASC
```

## This Week's Tasks (Tasks Plugin)
```tasks
not done
due after 2025-09-08
due before 2025-09-16
```

## This Month's Tasks (Dataview)
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-09-01) AND due <= date(2025-09-31)
SORT due ASC
```

## This Month's Tasks (Tasks Plugin)
```tasks
not done
due after 2025-09-01
due before 2025-10-01
```

## This Quarter's Tasks

```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-01-01) AND due <= date(2025-12-31)
SORT due ASC
```

## Overdue Tasks
```tasks
not done
due before 2025-09-09
```

## Upcoming Tasks (Next 7 Days)
```tasks
not done
due after 2025-09-09
due before 2025-09-16
```

## All Incomplete Tasks (No Due Date Required)
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed
SORT file.name ASC
LIMIT 20
```

## Church Administration Tasks (With Due Dates)
```dataview
TASK
FROM "2-Areas" OR "1-Projects"
WHERE !completed AND due AND (contains(tags, "church") OR contains(tags, "admin") OR contains(tags, "hall-hire") OR contains(tags, "finance"))
SORT due ASC
LIMIT 10
```

## Church Administration Tasks (All)
```dataview
TASK
FROM "2-Areas" OR "1-Projects"
WHERE !completed AND (contains(tags, "church") OR contains(tags, "admin") OR contains(tags, "hall-hire") OR contains(tags, "finance"))
SORT file.name ASC
LIMIT 15
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Today's Events & Reminders
```dataview
TABLE WITHOUT ID
  file.link as "Event",
  time as "Time",
  location as "Location",
  description as "Description"
FROM "Events&Meeting Dates" OR "2-Areas/0-Monthly-Tasks&Reports"
WHERE date = date(2025-09-09)
SORT time ASC
```

## Weekly Recurring Events (Today: Tuesday)
```dataview
TABLE WITHOUT ID
  file.link as "Activity",
  time as "Time",
  location as "Location",
  description as "Notes"
FROM "Events&Meeting Dates"
WHERE contains(tags, "recurring") AND contains(file.content, "Tuesday")
```

## Create New Note
- [[2025-09-09 Meeting|Create New Meeting]]
- [[2025-09-09 Task|Create New Task]]
- [[2025-09-09 Hall Hire|Create Hall Hire Booking]]
- [[2025-09-09 Finance|Create Finance Note]]

## Related
- [[Daily Notes TOC]]
- [[2025-09|Monthly Overview]]
- [[2025-W37|Weekly Overview]]
- [[Comprehensive Administrative Procedures Guide]]
- [[Home]]
