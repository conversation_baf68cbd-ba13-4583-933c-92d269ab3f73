---
creation_date: 2025-07-03 11:28
modification_date: Thursday 3rd July 2025 11:28:01
type: daily
date: 2025-07-03
day_of_week: Thursday
week: 2025-W27
month: 2025-07
tags: [daily, 2025-07]
mood: ""
energy_level: ""
weather: ""
location: ""
---

# 2025-07-03 - Thursday

<< [[2025-07-02]] | [[2025-07-04]] >>

> **🔄 Template not processed?** Click here: [Process Template](obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater)

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
-

## Tasks for Today
#todo #tasks #outstanding
### ✨ Tasks for Today

- [ ] 
- [ ] 
- [ ] 

## Church Matters
<!-- Church-related activities, meetings, pastoral care -->
-

## Hall <PERSON>re
<!-- Hall bookings, inquiries, maintenance -->
-

## Administration
<!-- Administrative tasks, correspondence, documentation -->
- Create/Update a Reference poster of weekly activities.
	- Maybe even expand to the annual activities as there have been some tough spots.
- 

## Finance & Banking
<!-- Banking, donations, payments, reconciliation -->
Add to the weekly procedures board:
1. square
2. chec bankings
3. 
4. Setting up payments
	1. adding the payment details to kirstys email as i go
	2. printing bank transfers

##### #Susan's #Procedures for #payment from email #27/06
Good afternoon jordan,
	Peter had picked up the dockets today for me.
	
	He said they were all on your desk. As you authorized or set these up yesterday they should have been in the data entry tray.
	
	The procedure as discussed yesterday
	
	1. Get the invoice posted or email to authorize payment - these should be paper copies
	
	2. Print bank authorisation sheet as you set it up
	
	3..Stamp as paid with the date.
	
	4.staple together
	
	3. Put in the data entry tray.
	
	4. If invoices were received on email transfer them to the paid folder - to avoid double payments.
	
	That way they are then off your desk. 
	
	Each payment must be printed separately.
	
	I do not have 
	
	5. the telstra bill that was received in the post - it is blue in colour
	
	6. The authorisation for the day camp payment to Kirsty - nor the email request for payment
	
	7. The additional payment to the other person - should have the email where the account details are supplied and the bank authorisation as you do them.
	
	SUSAN
## Safe Church
<!-- Compliance, training, documentation -->
-

## Follow-ups
<!-- Items requiring follow-up action -->
- [ ] 
- [ ] 

## Journal
<!-- How was your day? What happened? What did you learn? -->
-

## Notes
<!-- Any other notes or information -->
-

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-07-03)
SORT time ASC
```

## Today's Tasks Overview
```tasks
not done
due on 2025-07-03
```

## Tasks from This Note
```dataview
TASK
FROM "cruca-documentation-git/00-Daily Notes/2025-07-03.md"
WHERE !completed
```

## This Week's Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-06-29) AND due <= date(2025-07-05)
SORT due ASC
```

## This Month's Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-07-01) AND due <= date(2025-07-31)
SORT due ASC
```

## This Quarter's Tasks

```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-07-01) AND due <= date(2025-09-30)
SORT due ASC
```

## Overdue Tasks
```tasks
not done
due before 2025-07-03
```

## Upcoming Tasks (Next 7 Days)
```tasks
not done
due after 2025-07-03
due before 2025-07-10
```

## Church Administration Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects"
WHERE !completed AND (contains(tags, "church") OR contains(tags, "admin") OR contains(tags, "hall-hire") OR contains(tags, "finance"))
SORT due ASC
LIMIT 10
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[2025-07-03 Meeting|Create New Meeting]]
- [[2025-07-03 Task|Create New Task]]
- [[2025-07-03 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-07|Monthly Overview]]
- [[2025-W27|Weekly Overview]]
- [[Tasks]]
- [[Home]]
