---
<<<<<<< HEAD:00-Daily Notes/2025-07-11.md
creation_date: 2025-07-11 10:58
modification_date: Friday 11th July 2025 10:58:26
type: daily
date: 2025-07-11
day_of_week: Friday
week: 2025-W28
month: 2025-07
tags: [daily, 2025-07]
=======
creation_date: 2025-06-19 15:50
modification_date: Thursday 19th June 2025 15:50:56
type: daily
date: 2025-06-19
day_of_week: Thursday
week: 2025-W25
month: 2025-06
tags: [daily, 2025-06, cruca]
>>>>>>> origin/main:0-Journals-Daily Notes/00-Daily Notes/2025-07-11.md
mood: ""
energy_level: ""
weather: ""
location: ""
---

<<<<<<< HEAD:00-Daily Notes/2025-07-11.md
# 2025-07-11 - Friday

<< [[2025-07-10]] | [[2025-07-12]] >>
=======
# 2025-06-19 - Thursday

<< [[2025-06-18]] | [[2025-06-20]] >>
>>>>>>> origin/main:0-Journals-Daily Notes/00-Daily Notes/2025-07-11.md

> **🔄 Template not processed?** Click here: [Process Template](obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater)

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
<<<<<<< HEAD:00-Daily Notes/2025-07-11.md
-

## Tasks for Today
#todo #tasks #outstanding
### ✨ Tasks for Today

- [ ]
- [ ]
- [ ]

## Church Matters
<!-- Church-related activities, meetings, pastoral care -->
-

## Hall Hire
<!-- Hall bookings, inquiries, maintenance -->
-

## Administration
<!-- Administrative tasks, correspondence, documentation -->
-

## Finance & Banking
<!-- Banking, donations, payments, reconciliation -->
-

## Safe Church
<!-- Compliance, training, documentation -->
-

## Follow-ups
<!-- Items requiring follow-up action -->
- [ ]
- [ ]

## Journal
<!-- How was your day? What happened? What did you learn? -->
-

## Notes
<!-- Any other notes or information -->
-
=======
- 

## Tasks for Today
#todo #tasks #outstanding
<<<<<<< HEAD
### ✨ Tasks for Today

=======
### ✨ Tasks for Today

>>>>>>> origin/main
- [ ] 
- [ ] 
- [ ] 

## Bulletin
<!-- Church-related activities, meetings, pastoral care, services -->
- Jason RE handwritten notice for the 2025 elections for  upper Caboolture UCA
	- ```
	  thank you for all members of Beachmere and Caboolture who voted for elders Church representatives and other church officers last Sunday.
	  
	  We wish to remind all upper Caboolture members that your elections are this Sunday 13th July whilst Fay Mccaferty and Joy Misters have another one year term as elders. You are voting for Fay Mccaferty as Church representative.
	  
	  We will issue full election results in next week's bulletin. Church Council.

## Hall Hire
<!-- Hall bookings, inquiries, maintenance, key management -->
- 

## Administration
<!-- Administrative tasks, correspondence, documentation, reports -->
- 

## Finance & Banking
<!-- Banking, donations, payments, reconciliation, invoices -->
- 

## Safe Church
<!-- Compliance, training, documentation, Blue Card updates -->
- 

## Communication
<!-- Emails, phone calls, website updates, social media -->
- 

## Facilities & Maintenance
<!-- Building maintenance, equipment, cleaning, security -->
- 

## Follow-ups
<!-- Items requiring follow-up action with specific people/organizations -->
- [ ] 
- [ ] 

## Journal
<!-- How was your day? What happened? What did you learn? -->
- 

## Notes
<!-- Any other notes or information -->
- 
>>>>>>> origin/main:0-Journals-Daily Notes/00-Daily Notes/2025-07-11.md

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
<<<<<<< HEAD:00-Daily Notes/2025-07-11.md
WHERE date = date(2025-07-11)
=======
WHERE date = date(2025-06-19)
>>>>>>> origin/main:0-Journals-Daily Notes/00-Daily Notes/2025-07-11.md
SORT time ASC
```

## Today's Tasks Overview
```tasks
not done
<<<<<<< HEAD:00-Daily Notes/2025-07-11.md
due on 2025-07-11
```

## Tasks from This Note
```dataview
TASK
FROM "cruca-documentation-git/00-Daily Notes/2025-07-11.md"
WHERE !completed
```

## This Week's Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-07-06) AND due <= date(2025-07-12)
SORT due ASC
```

## This Month's Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-07-01) AND due <= date(2025-07-31)
SORT due ASC
```

=======
due on 2025-06-19
```

## Tasks from This Note (All)
```dataview
TASK
FROM "CRUCA-Church-Vault-git/Templates/Routine Administrative Procedures.md"
WHERE !completed
```

## Tasks from This Note (With Due Dates)
```dataview
TASK
FROM "CRUCA-Church-Vault-git/Templates/Routine Administrative Procedures.md"
WHERE !completed AND due
SORT due ASC
```

## This Week's Tasks (Dataview)
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-06-15) AND due <= date(2025-06-21)
SORT due ASC
```

## This Week's Tasks (Tasks Plugin)
```tasks
not done
due after 2025-06-14
due before 2025-06-22
```

## This Month's Tasks (Dataview)
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-06-01) AND due <= date(2025-06-31)
SORT due ASC
```

## This Month's Tasks (Tasks Plugin)
```tasks
not done
due after 2025-06-01
due before 2025-07-01
```

>>>>>>> origin/main:0-Journals-Daily Notes/00-Daily Notes/2025-07-11.md
## This Quarter's Tasks

```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
<<<<<<< HEAD:00-Daily Notes/2025-07-11.md
WHERE !completed AND due >= date(2025-07-01) AND due <= date(2025-09-30)
=======
WHERE !completed AND due >= date(2025-04-01) AND due <= date(2025-06-30)
>>>>>>> origin/main:0-Journals-Daily Notes/00-Daily Notes/2025-07-11.md
SORT due ASC
```

## Overdue Tasks
```tasks
not done
<<<<<<< HEAD:00-Daily Notes/2025-07-11.md
due before 2025-07-11
=======
due before 2025-06-19
>>>>>>> origin/main:0-Journals-Daily Notes/00-Daily Notes/2025-07-11.md
```

## Upcoming Tasks (Next 7 Days)
```tasks
not done
<<<<<<< HEAD:00-Daily Notes/2025-07-11.md
due after 2025-07-11
due before 2025-07-18
```

## Church Administration Tasks
=======
due after 2025-06-19
due before 2025-06-26
```

## All Incomplete Tasks (No Due Date Required)
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed
SORT file.name ASC
LIMIT 20
```

## Church Administration Tasks (With Due Dates)
```dataview
TASK
FROM "2-Areas" OR "1-Projects"
WHERE !completed AND due AND (contains(tags, "church") OR contains(tags, "admin") OR contains(tags, "hall-hire") OR contains(tags, "finance"))
SORT due ASC
LIMIT 10
```

## Church Administration Tasks (All)
>>>>>>> origin/main:0-Journals-Daily Notes/00-Daily Notes/2025-07-11.md
```dataview
TASK
FROM "2-Areas" OR "1-Projects"
WHERE !completed AND (contains(tags, "church") OR contains(tags, "admin") OR contains(tags, "hall-hire") OR contains(tags, "finance"))
<<<<<<< HEAD:00-Daily Notes/2025-07-11.md
SORT due ASC
LIMIT 10
=======
SORT file.name ASC
LIMIT 15
>>>>>>> origin/main:0-Journals-Daily Notes/00-Daily Notes/2025-07-11.md
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

<<<<<<< HEAD:00-Daily Notes/2025-07-11.md
## Create New Note
- [[2025-07-11 Meeting|Create New Meeting]]
- [[2025-07-11 Task|Create New Task]]
- [[2025-07-11 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-07|Monthly Overview]]
- [[2025-W28|Weekly Overview]]
- [[Tasks]]
=======
## Today's Events & Reminders
```dataview
TABLE WITHOUT ID
  file.link as "Event",
  time as "Time",
  location as "Location",
  description as "Description"
FROM "Events&Meeting Dates" OR "2-Areas/0-Monthly-Tasks&Reports"
WHERE date = date(2025-06-19)
SORT time ASC
```

## Weekly Recurring Events (Today: Thursday)
```dataview
TABLE WITHOUT ID
  file.link as "Activity",
  time as "Time",
  location as "Location",
  description as "Notes"
FROM "Events&Meeting Dates"
WHERE contains(tags, "recurring") AND contains(file.content, "Thursday")
```

## Create New Note
- [[2025-06-19 Meeting|Create New Meeting]]
- [[2025-06-19 Task|Create New Task]]
- [[2025-06-19 Hall Hire|Create Hall Hire Booking]]
- [[2025-06-19 Finance|Create Finance Note]]

## Related
- [[Daily Notes TOC]]
- [[2025-06|Monthly Overview]]
- [[2025-W25|Weekly Overview]]
- [[Comprehensive Administrative Procedures Guide]]
>>>>>>> origin/main:0-Journals-Daily Notes/00-Daily Notes/2025-07-11.md
- [[Home]]
