#!/usr/bin/env python3
"""
CRUCA Documentation Embedding Script
Embeds project files into Qdrant vector database directly
"""

import os
import json
import time
import uuid
from pathlib import Path
from typing import List, Dict, Any

try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, PointStruct
    from sentence_transformers import SentenceTransformer
except ImportError:
    print("Installing required packages...")
    os.system("pip3 install qdrant-client sentence-transformers")
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, PointStruct
    from sentence_transformers import SentenceTransformer

class CRUCAEmbedder:
    def __init__(self, qdrant_url: str = "http://localhost:6333"):
        self.qdrant_url = qdrant_url
        self.collection_name = "cruca-documentation"
        self.embedded_files = []
        self.failed_files = []

        # Initialize Qdrant client
        self.client = QdrantClient(url=qdrant_url)

        # Initialize embedding model
        print("🔄 Loading embedding model...")
        self.model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
        print("✅ Embedding model loaded")

    def ensure_collection_exists(self):
        """Create collection if it doesn't exist"""
        try:
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]

            if self.collection_name not in collection_names:
                print(f"🔄 Creating collection: {self.collection_name}")
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(size=384, distance=Distance.COSINE)
                )
                print(f"✅ Collection created: {self.collection_name}")
            else:
                print(f"✅ Collection exists: {self.collection_name}")

        except Exception as e:
            print(f"❌ Error with collection: {e}")
            return False
        return True

    def read_file_content(self, file_path: Path) -> str:
        """Read file content safely"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
            return ""

    def store_document(self, content: str, metadata: Dict[str, Any]) -> bool:
        """Store document in Qdrant directly"""
        try:
            # Generate embedding
            embedding = self.model.encode(content).tolist()

            # Create point
            point_id = str(uuid.uuid4())
            point = PointStruct(
                id=point_id,
                vector=embedding,
                payload={
                    "content": content,
                    **metadata
                }
            )

            # Store in Qdrant
            self.client.upsert(
                collection_name=self.collection_name,
                points=[point]
            )

            print(f"✅ Stored: {metadata.get('file_path', 'unknown')}")
            return True

        except Exception as e:
            print(f"❌ Exception storing document: {e}")
            return False
    
    def embed_file(self, file_path: Path, category: str = "documentation") -> bool:
        """Embed a single file"""
        print(f"📄 Processing: {file_path}")
        
        content = self.read_file_content(file_path)
        if not content.strip():
            print(f"⚠️  Skipping empty file: {file_path}")
            return False
        
        # Create metadata
        metadata = {
            "file_path": str(file_path),
            "file_name": file_path.name,
            "category": category,
            "file_type": file_path.suffix,
            "size": len(content),
            "embedded_at": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # Store in Qdrant
        success = self.store_document(content, metadata)
        
        if success:
            self.embedded_files.append(str(file_path))
        else:
            self.failed_files.append(str(file_path))
            
        return success
    
    def embed_priority_files(self):
        """Embed high-priority documentation files"""
        priority_files = [
            # Core documentation
            ("README.md", "core"),
            ("AGENTS.MD", "core"),
            ("Home.md", "core"),
            
            # Table of Contents files
            ("1-Projects/Projects TOC.md", "navigation"),
            ("2-Areas/Areas TOC.md", "navigation"),
            ("3-Resources/Resources TOC.md", "navigation"),
            ("Templates/Templates TOC.md", "navigation"),
            
            # Key administration files
            ("2-Areas/Administration Overview.md", "administration"),
            ("2-Areas/Church Administration.md", "administration"),
            ("2-Areas/Social Media Automation tools.md", "automation"),
            
            # Important resources
            ("3-Resources/Report Generator Assistant Prompt (Augment Ai).md", "automation"),
            ("3-Resources/Templater Auto-Processing Setup Guide.md", "automation"),
            ("3-Resources/Task Format Guide - Dataview vs Tasks Plugin.md", "documentation"),
            ("3-Resources/Hall Hire Procedures.md", "procedures"),
            
            # Office procedures
            ("Office-Procedures/Office-Procedures-Manual.md", "procedures"),
            ("Office-Procedures/Finance-Procedures.md", "procedures"),
        ]
        
        print("🚀 Starting priority file embedding...")
        
        for file_path, category in priority_files:
            full_path = Path(file_path)
            if full_path.exists():
                self.embed_file(full_path, category)
                time.sleep(1)  # Rate limiting
            else:
                print(f"⚠️  File not found: {file_path}")
    
    def embed_directory_files(self, directory: str, category: str, pattern: str = "*.md"):
        """Embed all markdown files in a directory"""
        print(f"📁 Processing directory: {directory}")
        
        dir_path = Path(directory)
        if not dir_path.exists():
            print(f"⚠️  Directory not found: {directory}")
            return
        
        md_files = list(dir_path.rglob(pattern))
        print(f"Found {len(md_files)} files in {directory}")
        
        for file_path in md_files:
            # Skip backup and temporary files
            if any(skip in str(file_path) for skip in ['.edtz', '_BACKUP_', '_BASE_', '_LOCAL_', '_REMOTE_']):
                continue
                
            self.embed_file(file_path, category)
            time.sleep(0.5)  # Rate limiting
    
    def run_full_embedding(self):
        """Run complete embedding process"""
        print("🎯 CRUCA Documentation Embedding Started")
        print("=" * 50)

        # Step 0: Ensure collection exists
        if not self.ensure_collection_exists():
            print("❌ Failed to create/verify collection")
            return

        # Step 1: Priority files
        self.embed_priority_files()

        # Step 2: Templates
        self.embed_directory_files("Templates", "templates")

        # Step 3: Projects
        self.embed_directory_files("1-Projects", "projects")

        # Step 4: Areas
        self.embed_directory_files("2-Areas", "areas")

        # Step 5: Resources
        self.embed_directory_files("3-Resources", "resources")

        # Step 6: Office Procedures
        self.embed_directory_files("Office-Procedures", "procedures")

        print("\n" + "=" * 50)
        print("📊 EMBEDDING SUMMARY")
        print("=" * 50)
        print(f"✅ Successfully embedded: {len(self.embedded_files)} files")
        print(f"❌ Failed to embed: {len(self.failed_files)} files")

        if self.failed_files:
            print("\n❌ Failed files:")
            for file in self.failed_files:
                print(f"  - {file}")

        print(f"\n🎉 Embedding complete!")

        # Test search functionality
        self.test_search()

    def test_search(self):
        """Test search functionality"""
        print("\n" + "=" * 50)
        print("🔍 TESTING SEARCH FUNCTIONALITY")
        print("=" * 50)

        test_queries = [
            "church administration procedures",
            "hall hire booking",
            "social media automation",
            "templater setup guide"
        ]

        for query in test_queries:
            print(f"\n🔍 Query: '{query}'")
            try:
                # Generate query embedding
                query_embedding = self.model.encode(query).tolist()

                # Search in Qdrant
                results = self.client.search(
                    collection_name=self.collection_name,
                    query_vector=query_embedding,
                    limit=3
                )

                if results:
                    for i, result in enumerate(results, 1):
                        file_path = result.payload.get('file_path', 'Unknown')
                        score = result.score
                        print(f"  {i}. {file_path} (score: {score:.3f})")
                else:
                    print("  No results found")

            except Exception as e:
                print(f"  ❌ Search error: {e}")

if __name__ == "__main__":
    embedder = CRUCAEmbedder()
    embedder.run_full_embedding()
