# Preventing Driver Updates on Windows 11 Home

## For NVIDIA Graphics Drivers

### Method 1: Windows Update Advanced Options
1. Open **Settings** (Win+I)
2. Go to **Windows Update**
3. Click on **Advanced options**
4. Toggle ON **Receive updates for other Microsoft products**
5. Scroll down and click on **Optional updates**
6. Look for **Driver updates** section
7. Uncheck any NVIDIA driver updates
8. Close Settings

### Method 2: Show or Hide Updates Tool
1. Download the [Show or Hide Updates Troubleshooter](https://support.microsoft.com/en-us/topic/how-to-temporarily-prevent-a-windows-update-from-reinstalling-in-windows-349ff0f9-ebcf-4e64-b48a-d86fefec6e9d)
2. Run the downloaded `.diagcab` file
3. Select **Hide updates**
4. Check any NVIDIA driver updates from the list
5. Click **Next** and follow the prompts to complete

### Method 3: Update Device Installation Settings
1. Press **Win+X** and select **Device Manager**
2. Click on the **View** menu and select **Devices by type**
3. Expand **Display adapters**
4. Right-click on **NVIDIA GeForce RTX 2060**
5. Select **Properties**
6. Go to the **Driver** tab
7. Click **Update Driver**
8. Select **Browse my computer for drivers**
9. Select **Let me pick from a list of available drivers on my computer**
10. Select the known working driver (564.xx series)
11. Click **Next** to install
12. When asked to update in the future, select **No**

### Method 4: Registry Editor Method
1. Press **Win+R** and type `regedit`
2. Navigate to:
   ```
   HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate
   ```
3. If the **WindowsUpdate** key doesn't exist, right-click on **Windows** and create a new Key named **WindowsUpdate**
4. Right-click in the right pane and select **New > DWORD (32-bit) Value**
5. Name it **ExcludeWUDriversInQualityUpdate**
6. Double-click it and set the value to **1**
7. Create another DWORD value named **DriverBlockList**
8. Double-click it and set its value to **1**
9. Restart your computer

### Method 5: Use Windows 11 Metered Connection
1. Open **Settings** (Win+I)
2. Go to **Network & internet**
3. Select your connection (Wi-Fi or Ethernet)
4. Toggle ON **Metered connection**
5. This limits Windows Update from downloading large updates including drivers

### Method 6: Install DDU and Clean Install Specific Driver
1. Boot into Safe Mode:
   - Open **Settings** > **System** > **Recovery**
   - Under **Advanced startup** click **Restart now**
   - Select **Troubleshoot** > **Advanced options** > **Startup Settings** > **Restart**
   - After restart, press **4** for Safe Mode
2. Download and run [Display Driver Uninstaller (DDU)](https://www.wagnardsoft.com/)
3. Let DDU remove all NVIDIA drivers and reboot
4. Download the specific working driver (564.xx) from [NVIDIA's website](https://www.nvidia.com/Download/Find.aspx)
5. Install the downloaded driver
6. During installation, select **Custom Installation**
7. Check **Perform a clean installation**
8. Make sure **Enable automatic driver updates** is UNCHECKED

### Method 7: Create a Batch File to Disable the NVIDIA Driver Update Service
1. Create a new text file on your desktop
2. Paste the following content:
   ```batch
   @echo off
   echo Stopping NVIDIA Update Service
   net stop NVUpdateService
   echo Setting service to disabled
   sc config NVUpdateService start= disabled
   echo Done!
   pause
   ```
3. Save the file as `DisableNVIDIAUpdates.bat`
4. Right-click the file and select **Run as administrator**
5. This will disable the NVIDIA update service 