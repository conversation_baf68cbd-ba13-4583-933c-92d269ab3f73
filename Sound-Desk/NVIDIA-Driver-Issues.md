# NVIDIA Driver Blank Screen Issue - Troubleshooting Guide

## Table of Contents
- [[#Problem Description|Problem Description]]
- [[#Affected Hardware|Affected Hardware]]
- [[#Causes|Causes]]
- [[#Immediate Solutions|Immediate Solutions]]
- [[#Preventing Windows Driver Updates|Preventing Windows Driver Updates]]
- [[#Installing Specific Driver Versions|Installing Specific Driver Versions]]
- [[#Advanced Troubleshooting|Advanced Troubleshooting]]

## Problem Description
When updating to the latest NVIDIA graphics card driver, the display goes completely blank during Windows operation. The system appears to be running, but no display output is visible. This typically occurs after Windows automatically updates the graphics driver.

## Affected Hardware
- **Computer Model**: OMEN by HP Obelisk Desktop 875-0060a
- **Graphics Card**: NVIDIA GeForce RTX 2060
- **Working Driver Version**: ~564 series
- **Problematic Driver**: Latest NVIDIA drivers

## Causes
Several factors may cause this issue:
1. **Incompatibility**: Newer drivers may have incompatibility with specific hardware combinations
2. **Power management**: Recent drivers might handle power states differently
3. **Monitor connectivity**: Display port/HDMI handling changes in newer drivers
4. **HP-specific customizations**: OEM-specific hardware configurations that conflict with generic NVIDIA drivers

## Immediate Solutions
1. **Boot into Safe Mode**:
   - Restart the computer
   - Press F8 during startup (or hold Shift while clicking Restart)
   - Select "Safe Mode"

2. **Disable the Graphics Card**:
   - In Safe Mode, open Device Manager
   - Expand "Display adapters"
   - Right-click on the NVIDIA GeForce RTX 2060
   - Select "Disable device"
   - Restart normally to use basic display adapter

3. **Rollback Driver**:
   - In Safe Mode, open Device Manager
   - Expand "Display adapters"
   - Right-click on the NVIDIA GeForce RTX 2060
   - Select "Properties"
   - Go to "Driver" tab
   - Click "Roll Back Driver"

## Preventing Windows Driver Updates
To prevent Windows from updating the NVIDIA driver:

1. **Group Policy Editor** (Windows Pro/Enterprise):
   - Press Win+R, type `gpedit.msc`
   - Navigate to Computer Configuration > Administrative Templates > Windows Components > Windows Update
   - Enable "Do not include drivers with Windows Updates"

2. **Show/Hide Updates Tool**:
   - Download the "Show or hide updates" troubleshooter from Microsoft
   - Run the tool and hide the NVIDIA driver updates

3. **Device Installation Settings**:
   - Open Control Panel > System > Advanced system settings
   - Click "Hardware" tab > "Device Installation Settings"
   - Select "No (your device might not work as expected)"
   - Click Save Changes

4. **Windows Update Registry Modification**:
   - Press Win+R, type `regedit`
   - Navigate to `HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate`
   - Create a new DWORD value named "ExcludeWUDriversInQualityUpdate"
   - Set its value to 1

## Installing Specific Driver Versions
To install version ~564 of the NVIDIA driver:

1. **Download the specific driver**:
   - Visit [NVIDIA Driver Archive](https://www.nvidia.com/Download/Find.aspx)
   - Select GeForce RTX 2060, Windows 10 64-bit
   - Find driver version 564.xx
   - Download the installer

2. **Clean Installation**:
   - Boot into Safe Mode
   - Uninstall current driver using Display Driver Uninstaller (DDU)
   - Restart in Safe Mode
   - Install the downloaded 564.xx driver
   - Check "Perform a clean installation"
   - Complete installation and restart

3. **Prevent Auto-Update**:
   - Open NVIDIA Control Panel
   - Navigate to "Desktop" menu
   - Uncheck "Enable automatic updates"

## Advanced Troubleshooting
If the issue persists:

1. **BIOS Settings**:
   - Enter BIOS (typically F10 for HP)
   - Check if PCIe configuration options need adjustment
   - Ensure primary display is set to PCIe/PEG

2. **Power Management**:
   - In NVIDIA Control Panel, set power management to "Prefer maximum performance"
   - Check Windows power plan settings

3. **Alternative Display Outputs**:
   - Try different outputs (HDMI, DisplayPort, DVI)
   - Try different monitors if available

4. **HP Support**:
   - Check HP support website for specific driver versions for the Obelisk 875-0060a
   - HP may offer customized drivers better suited for this system

5. **Event Log Analysis**:
   - Check Windows Event Viewer for driver-related errors
   - Look under "System" and "Application" logs for NVIDIA-related events 